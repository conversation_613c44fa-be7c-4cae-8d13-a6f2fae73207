// 页面内容生成模块

// 仪表盘页面
function getDashboardHTML() {
  return `
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">系统概览</h2>
                
                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100">总票据数</p>
                                <p id="totalInvoices" class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-file-invoice text-4xl text-blue-200"></i>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100">已开具</p>
                                <p id="issuedInvoices" class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-check-circle text-4xl text-green-200"></i>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-100">已交付</p>
                                <p id="deliveredInvoices" class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-paper-plane text-4xl text-yellow-200"></i>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100">总金额</p>
                                <p id="totalAmount" class="text-3xl font-bold">¥0</p>
                            </div>
                            <i class="fas fa-coins text-4xl text-purple-200"></i>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-4">票据类型分布</h3>
                        <canvas id="invoiceTypeChart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-4">最近开具趋势</h3>
                        <canvas id="invoiceTrendChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-4">快速操作</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button onclick="showPage('issue')" class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition">
                        <i class="fas fa-plus-circle text-2xl text-blue-600 mb-2"></i>
                        <span class="text-sm font-medium">开具票据</span>
                    </button>
                    
                    <button onclick="showPage('delivery')" class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition">
                        <i class="fas fa-paper-plane text-2xl text-green-600 mb-2"></i>
                        <span class="text-sm font-medium">票据交付</span>
                    </button>
                    
                    <button onclick="showPage('archive')" class="flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition">
                        <i class="fas fa-archive text-2xl text-yellow-600 mb-2"></i>
                        <span class="text-sm font-medium">存档查询</span>
                    </button>
                    
                    <button onclick="showPage('reports')" class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition">
                        <i class="fas fa-chart-bar text-2xl text-purple-600 mb-2"></i>
                        <span class="text-sm font-medium">统计报表</span>
                    </button>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-4">最近活动</h3>
                <div id="recentActivities" class="space-y-3">
                    <!-- 活动列表将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    `
}

function loadDashboardData() {
  const stats = dataManager.getStatistics()

  // 更新统计卡片
  document.getElementById('totalInvoices').textContent = stats.totalInvoices
  document.getElementById('issuedInvoices').textContent = stats.issuedInvoices
  document.getElementById('deliveredInvoices').textContent = stats.deliveredInvoices
  document.getElementById('totalAmount').textContent = formatAmount(stats.totalAmount)

  // 加载图表
  loadDashboardCharts()

  // 加载最近活动
  loadRecentActivities()
}

function loadDashboardCharts() {
  // 票据类型分布图
  const invoices = dataManager.getInvoices()
  const typeData = {}
  invoices.forEach(invoice => {
    typeData[invoice.type] = (typeData[invoice.type] || 0) + 1
  })

  const typeChart = new Chart(document.getElementById('invoiceTypeChart'), {
    type: 'doughnut',
    data: {
      labels: Object.keys(typeData),
      datasets: [
        {
          data: Object.values(typeData),
          backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
    },
  })

  // 趋势图（模拟数据）
  const trendChart = new Chart(document.getElementById('invoiceTrendChart'), {
    type: 'line',
    data: {
      labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
      datasets: [
        {
          label: '开具数量',
          data: [12, 19, 15, 25, 22, 30],
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    },
  })
}

function loadRecentActivities() {
  const invoices = dataManager.getInvoices()
  const recentInvoices = invoices
    .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
    .slice(0, 5)

  const activitiesHTML = recentInvoices
    .map(
      invoice => `
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-file-invoice text-white text-sm"></i>
                </div>
                <div>
                    <p class="font-medium">${invoice.patientName} - ${invoice.type}票据</p>
                    <p class="text-sm text-gray-600">${formatDate(invoice.createTime)}</p>
                </div>
            </div>
            <div class="text-right">
                <p class="font-medium">${formatAmount(invoice.amount)}</p>
                <p class="text-sm text-gray-600">${invoice.status}</p>
            </div>
        </div>
    `
    )
    .join('')

  document.getElementById('recentActivities').innerHTML =
    activitiesHTML || '<p class="text-gray-500 text-center py-4">暂无最近活动</p>'
}

// 单位管理页面
function getUnitsHTML() {
  return `
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-800">单位信息管理</h2>
                    <button onclick="showAddUnitModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-plus mr-2"></i>添加单位
                    </button>
                </div>
            </div>

            <div class="p-6">
                <!-- 搜索栏 -->
                <div class="mb-4">
                    <input type="text" id="unitSearch" placeholder="搜索单位名称、地址或联系人..."
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           onkeyup="searchTable('unitSearch', 'unitsTable')">
                </div>

                <!-- 单位列表 -->
                <div class="overflow-x-auto">
                    <table id="unitsTable" class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-left">单位ID</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">单位名称</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">地址</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">联系人</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">联系电话</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">创建时间</th>
                                <th class="border border-gray-300 px-4 py-3 text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody id="unitsTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `
}

function loadUnitsData() {
  const units = dataManager.getUnits()
  const tbody = document.getElementById('unitsTableBody')

  tbody.innerHTML = units
    .map(
      unit => `
        <tr class="hover:bg-gray-50">
            <td class="border border-gray-300 px-4 py-3">${unit.id}</td>
            <td class="border border-gray-300 px-4 py-3 font-medium">${unit.name}</td>
            <td class="border border-gray-300 px-4 py-3">${unit.address}</td>
            <td class="border border-gray-300 px-4 py-3">${unit.contact}</td>
            <td class="border border-gray-300 px-4 py-3">${unit.phone || '-'}</td>
            <td class="border border-gray-300 px-4 py-3">${formatDate(unit.createTime)}</td>
            <td class="border border-gray-300 px-4 py-3 text-center">
                <button onclick="editUnit('${
                  unit.id
                }')" class="text-blue-600 hover:text-blue-800 mr-2">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="deleteUnit('${unit.id}')" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `
    )
    .join('')
}

function showAddUnitModal() {
  const content = `
        <form id="unitForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">单位名称 *</label>
                <input type="text" id="unitName" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">地址 *</label>
                <input type="text" id="unitAddress" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">联系人 *</label>
                <input type="text" id="unitContact" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">联系电话</label>
                <input type="tel" id="unitPhone" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    取消
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    保存
                </button>
            </div>
        </form>
    `

  showModal('添加单位', content)

  document.getElementById('unitForm').addEventListener('submit', function (e) {
    e.preventDefault()
    saveUnit()
  })
}

function saveUnit(editId = null) {
  const unitData = {
    name: document.getElementById('unitName').value.trim(),
    address: document.getElementById('unitAddress').value.trim(),
    contact: document.getElementById('unitContact').value.trim(),
    phone: document.getElementById('unitPhone').value.trim(),
  }

  if (!unitData.name || !unitData.address || !unitData.contact) {
    showMessage('请填写所有必填字段', 'error')
    return
  }

  try {
    if (editId) {
      dataManager.updateUnit(editId, unitData)
      showMessage('单位信息更新成功', 'success')
    } else {
      dataManager.addUnit(unitData)
      showMessage('单位添加成功', 'success')
    }

    closeModal()
    loadUnitsData()
  } catch (error) {
    showMessage('操作失败：' + error.message, 'error')
  }
}

function editUnit(id) {
  const unit = dataManager.getById('units', id)
  if (!unit) {
    showMessage('单位不存在', 'error')
    return
  }

  const content = `
        <form id="unitForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">单位名称 *</label>
                <input type="text" id="unitName" value="${
                  unit.name
                }" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">地址 *</label>
                <input type="text" id="unitAddress" value="${
                  unit.address
                }" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">联系人 *</label>
                <input type="text" id="unitContact" value="${
                  unit.contact
                }" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">联系电话</label>
                <input type="tel" id="unitPhone" value="${
                  unit.phone || ''
                }" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    取消
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    更新
                </button>
            </div>
        </form>
    `

  showModal('编辑单位', content)

  document.getElementById('unitForm').addEventListener('submit', function (e) {
    e.preventDefault()
    saveUnit(id)
  })
}

function deleteUnit(id) {
  showConfirm('确定要删除这个单位吗？此操作不可撤销。', () => {
    try {
      dataManager.deleteUnit(id)
      showMessage('单位删除成功', 'success')
      loadUnitsData()
    } catch (error) {
      showMessage('删除失败：' + error.message, 'error')
    }
  })
}

// 票据管理页面
function getInvoicesHTML() {
  return `
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-800">票据管理</h2>
                    <div class="flex space-x-2">
                        <button onclick="showPage('issue')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            <i class="fas fa-plus mr-2"></i>开具票据
                        </button>
                        <button onclick="exportInvoices()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                            <i class="fas fa-download mr-2"></i>导出数据
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <!-- 筛选栏 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <input type="text" id="invoiceSearch" placeholder="搜索患者姓名或票据号..."
                           class="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                           onkeyup="filterInvoices()">

                    <select id="typeFilter" class="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" onchange="filterInvoices()">
                        <option value="">所有类型</option>
                        <option value="门诊">门诊</option>
                        <option value="住院">住院</option>
                    </select>

                    <select id="statusFilter" class="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" onchange="filterInvoices()">
                        <option value="">所有状态</option>
                        <option value="已开具">已开具</option>
                        <option value="已冲红">已冲红</option>
                        <option value="已作废">已作废</option>
                    </select>

                    <button onclick="resetFilters()" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                        <i class="fas fa-refresh mr-2"></i>重置筛选
                    </button>
                </div>

                <!-- 票据列表 -->
                <div class="overflow-x-auto">
                    <table id="invoicesTable" class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-left">票据号</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">患者姓名</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">类型</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">金额</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">状态</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">开具时间</th>
                                <th class="border border-gray-300 px-4 py-3 text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody id="invoicesTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div id="invoicesPagination" class="mt-4"></div>
            </div>
        </div>
    `
}

let invoicesPagination = null

function loadInvoicesData() {
  const invoices = dataManager.getInvoices()
  invoicesPagination = new Pagination(invoices, 10)
  renderInvoicesTable()
}

function renderInvoicesTable() {
  const currentData = invoicesPagination.getCurrentPageData()
  const tbody = document.getElementById('invoicesTableBody')

  tbody.innerHTML = currentData
    .map(
      invoice => `
        <tr class="hover:bg-gray-50">
            <td class="border border-gray-300 px-4 py-3 font-mono">${invoice.id}</td>
            <td class="border border-gray-300 px-4 py-3 font-medium">${invoice.patientName}</td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="px-2 py-1 rounded-full text-xs ${
                  invoice.type === '门诊'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-green-100 text-green-800'
                }">
                    ${invoice.type}
                </span>
            </td>
            <td class="border border-gray-300 px-4 py-3 font-medium">${formatAmount(
              invoice.amount
            )}</td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="px-2 py-1 rounded-full text-xs ${getStatusClass(invoice.status)}">
                    ${invoice.status}
                </span>
            </td>
            <td class="border border-gray-300 px-4 py-3">${formatDate(invoice.createTime)}</td>
            <td class="border border-gray-300 px-4 py-3 text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="viewInvoice('${
                      invoice.id
                    }')" class="text-blue-600 hover:text-blue-800" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="printInvoice('${
                      invoice.id
                    }')" class="text-green-600 hover:text-green-800" title="打印">
                        <i class="fas fa-print"></i>
                    </button>
                    ${
                      invoice.status === '已开具'
                        ? `
                        <button onclick="refundInvoice('${invoice.id}')" class="text-red-600 hover:text-red-800" title="冲红">
                            <i class="fas fa-undo"></i>
                        </button>
                    `
                        : ''
                    }
                </div>
            </td>
        </tr>
    `
    )
    .join('')

  // 更新分页
  document.getElementById('invoicesPagination').innerHTML =
    invoicesPagination.getPaginationHTML('goToInvoicePage')
}

function goToInvoicePage(page) {
  if (invoicesPagination.goToPage(page)) {
    renderInvoicesTable()
  }
}

function getStatusClass(status) {
  const classes = {
    已开具: 'bg-green-100 text-green-800',
    已冲红: 'bg-red-100 text-red-800',
    已作废: 'bg-gray-100 text-gray-800',
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function filterInvoices() {
  const searchTerm = document.getElementById('invoiceSearch').value.toLowerCase()
  const typeFilter = document.getElementById('typeFilter').value
  const statusFilter = document.getElementById('statusFilter').value

  let filteredInvoices = dataManager.getInvoices()

  if (searchTerm) {
    filteredInvoices = filteredInvoices.filter(
      invoice =>
        invoice.patientName.toLowerCase().includes(searchTerm) ||
        invoice.id.toLowerCase().includes(searchTerm)
    )
  }

  if (typeFilter) {
    filteredInvoices = filteredInvoices.filter(invoice => invoice.type === typeFilter)
  }

  if (statusFilter) {
    filteredInvoices = filteredInvoices.filter(invoice => invoice.status === statusFilter)
  }

  invoicesPagination = new Pagination(filteredInvoices, 10)
  renderInvoicesTable()
}

function resetFilters() {
  document.getElementById('invoiceSearch').value = ''
  document.getElementById('typeFilter').value = ''
  document.getElementById('statusFilter').value = ''
  loadInvoicesData()
}

function exportInvoices() {
  const invoices = dataManager.getInvoices()
  exportData(invoices, 'invoices_' + new Date().toISOString().slice(0, 10) + '.json')
  showMessage('票据数据导出成功', 'success')
}

function viewInvoice(id) {
  const invoice = dataManager.getById('invoices', id)
  if (!invoice) {
    showMessage('票据不存在', 'error')
    return
  }

  const content = `
        <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">票据号</label>
                    <p class="mt-1 text-sm text-gray-900 font-mono">${invoice.id}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">状态</label>
                    <p class="mt-1"><span class="px-2 py-1 rounded-full text-xs ${getStatusClass(
                      invoice.status
                    )}">${invoice.status}</span></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">患者姓名</label>
                    <p class="mt-1 text-sm text-gray-900">${invoice.patientName}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">身份证号</label>
                    <p class="mt-1 text-sm text-gray-900">${invoice.patientId || '-'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">联系电话</label>
                    <p class="mt-1 text-sm text-gray-900">${invoice.phone || '-'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">票据类型</label>
                    <p class="mt-1 text-sm text-gray-900">${invoice.type}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">金额</label>
                    <p class="mt-1 text-sm text-gray-900 font-medium">${formatAmount(
                      invoice.amount
                    )}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">开具时间</label>
                    <p class="mt-1 text-sm text-gray-900">${formatDate(invoice.createTime)}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">医生</label>
                    <p class="mt-1 text-sm text-gray-900">${invoice.doctor || '-'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">科室</label>
                    <p class="mt-1 text-sm text-gray-900">${invoice.department || '-'}</p>
                </div>
            </div>
            <div class="flex justify-end space-x-3 pt-4">
                <button onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    关闭
                </button>
                <button onclick="printInvoice('${
                  invoice.id
                }'); closeModal();" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    <i class="fas fa-print mr-2"></i>打印
                </button>
            </div>
        </div>
    `

  showModal('票据详情', content)
}

function printInvoice(id) {
  const invoice = dataManager.getById('invoices', id)
  if (!invoice) {
    showMessage('票据不存在', 'error')
    return
  }

  // 创建打印内容
  const printContent = `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px;">
                <h1 style="margin: 0; font-size: 24px;">医疗电子票据</h1>
                <p style="margin: 5px 0; color: #666;">票据号：${invoice.id}</p>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div>
                    <p><strong>患者姓名：</strong>${invoice.patientName}</p>
                    <p><strong>身份证号：</strong>${invoice.patientId || '-'}</p>
                    <p><strong>联系电话：</strong>${invoice.phone || '-'}</p>
                </div>
                <div>
                    <p><strong>票据类型：</strong>${invoice.type}</p>
                    <p><strong>医生：</strong>${invoice.doctor || '-'}</p>
                    <p><strong>科室：</strong>${invoice.department || '-'}</p>
                </div>
            </div>

            <div style="border: 1px solid #ddd; padding: 15px; margin-bottom: 20px;">
                <p style="font-size: 18px; margin: 0;"><strong>金额：${formatAmount(
                  invoice.amount
                )}</strong></p>
            </div>

            <div style="text-align: right; color: #666; font-size: 12px;">
                <p>开具时间：${formatDate(invoice.createTime)}</p>
                <p>状态：${invoice.status}</p>
            </div>
        </div>
    `

  const printWindow = window.open('', '_blank')
  printWindow.document.write(`
        <html>
            <head>
                <title>打印票据 - ${invoice.id}</title>
                <style>
                    body { margin: 0; padding: 20px; }
                    @media print {
                        body { margin: 0; }
                    }
                </style>
            </head>
            <body>
                ${printContent}
            </body>
        </html>
    `)
  printWindow.document.close()
  printWindow.print()

  showMessage('票据打印成功', 'success')
}

function refundInvoice(id) {
  showConfirm('确定要冲红这张票据吗？冲红后票据将无法恢复。', () => {
    try {
      dataManager.updateInvoice(id, { status: '已冲红' })
      showMessage('票据冲红成功', 'success')
      loadInvoicesData()
    } catch (error) {
      showMessage('冲红失败：' + error.message, 'error')
    }
  })
}
