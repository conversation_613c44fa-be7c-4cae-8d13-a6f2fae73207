# 医疗电子票据管理系统

一个完整的医疗电子票据管理系统，支持票据的全生命周期管理，包括开具、交付、存档、查询等功能。

## 🚀 功能特性

### 核心功能
- **票据管理**: 支持门诊/住院票据的开具、冲红、打印、补打
- **单位管理**: 医疗机构信息的维护和管理
- **用户管理**: 多角色用户系统（管理员、医生、财务）
- **权限控制**: 基于角色的页面访问权限控制
- **票据交付**: 支持短信、邮件、小程序等多渠道交付
- **存档查询**: 票据归档和历史查询功能
- **统计报表**: 丰富的数据统计和图表展示
- **小程序模拟**: 模拟微信小程序扫码取票功能

### 技术特性
- **响应式设计**: 支持桌面端和移动端访问
- **本地存储**: 使用 LocalStorage 进行数据持久化
- **模块化架构**: 清晰的代码结构和模块划分
- **现代UI**: 基于 Tailwind CSS 的现代化界面
- **图表支持**: 集成 Chart.js 进行数据可视化
- **二维码生成**: 支持票据二维码生成和扫描

## 📁 项目结构

```
medical-e-bill/
├── index.html              # 主页面
├── test.html              # 测试页面
├── README.md              # 项目说明
├── medical_invoice_prd.md # 产品需求文档
└── js/
    ├── data.js            # 数据管理模块
    ├── auth.js            # 认证和权限管理
    ├── app.js             # 主应用程序
    ├── pages.js           # 基础页面内容
    ├── pages-extended.js  # 扩展页面内容
    └── pages-final.js     # 最终页面内容
```

## 🛠️ 安装和运行

### 方法一：直接打开文件
1. 下载项目文件到本地
2. 直接在浏览器中打开 `index.html` 文件

### 方法二：使用本地服务器
1. 确保已安装 Python 或 Node.js
2. 在项目目录下运行以下命令之一：

```bash
# 使用 Python
python -m http.server 8000

# 使用 Node.js (需要先安装 http-server)
npx http-server -p 8000
```

3. 在浏览器中访问 `http://localhost:8000`

## 👥 用户角色和权限

### 测试账号
- **管理员**: `admin` (拥有所有权限)
- **医生**: `doctor1` (可开具票据、查看报表)
- **财务**: `finance1` (可管理票据、执行交付)

### 权限说明
- **管理员**: 系统所有功能的完全访问权限
- **医生**: 票据开具、查看、报表查询权限
- **财务**: 票据管理、交付、存档、报表权限

## 📋 使用指南

### 1. 登录系统
- 打开系统后会显示登录页面
- 输入用户名（如 `admin`）即可登录
- 系统会根据用户角色显示相应的功能菜单

### 2. 开具票据
1. 点击"开具票据"菜单
2. 填写患者信息（姓名、手机号等）
3. 选择票据类型（门诊/住院）
4. 填写就诊信息（科室、医生等）
5. 输入费用金额
6. 点击"开具票据"完成

### 3. 票据管理
- 在"票据管理"页面可以查看所有票据
- 支持搜索、筛选、分页功能
- 可以查看票据详情、打印、冲红等操作

### 4. 票据交付
1. 进入"票据交付"页面
2. 选择要交付的票据
3. 选择交付方式（短信、邮件、小程序等）
4. 配置接收方信息
5. 执行交付操作

### 5. 小程序取票
- 在"小程序取票"页面体验模拟功能
- 支持手机号查询和二维码扫描
- 可以生成票据二维码

### 6. 统计报表
- 查看系统统计数据
- 支持图表展示和数据导出
- 按科室、时间等维度分析

## 🧪 测试功能

系统提供了完整的测试页面：

1. 打开 `test.html` 页面
2. 点击各个测试按钮验证功能
3. 查看测试结果确保系统正常运行

测试包括：
- 数据管理功能测试
- 认证系统测试
- 票据操作测试
- 用户管理测试
- 系统完整性测试

## 📊 数据结构

### 票据数据结构
```json
{
  "id": "INV20250709-001",
  "patientName": "患者姓名",
  "patientId": "身份证号",
  "phone": "联系电话",
  "type": "门诊/住院",
  "amount": 200.0,
  "status": "已开具",
  "archived": false,
  "delivered": false,
  "createTime": "2025-07-09T10:00:00.000Z",
  "department": "科室",
  "doctor": "医生姓名"
}
```

### 用户数据结构
```json
{
  "username": "用户名",
  "name": "真实姓名",
  "role": "角色",
  "createTime": "创建时间"
}
```

## 🔧 自定义配置

### 添加新的科室
在 `js/pages-extended.js` 文件中的 `getIssueHTML` 函数里修改科室选项。

### 修改用户角色
在 `js/auth.js` 文件中的 `getRolePermissions` 函数里配置角色权限。

### 自定义样式
系统使用 Tailwind CSS，可以通过修改 HTML 中的 class 来调整样式。

## 🐛 故障排除

### 常见问题

1. **页面显示异常**
   - 检查浏览器是否支持现代 JavaScript 特性
   - 确保网络连接正常，CDN 资源能够加载

2. **数据丢失**
   - 数据存储在浏览器的 LocalStorage 中
   - 清除浏览器数据会导致数据丢失
   - 可以使用导出功能备份数据

3. **功能无法使用**
   - 检查用户权限是否足够
   - 确认是否正确登录系统

## 📝 开发说明

### 代码结构
- `data.js`: 数据层，负责 LocalStorage 操作
- `auth.js`: 认证层，处理用户登录和权限
- `app.js`: 应用层，主要的应用逻辑
- `pages-*.js`: 视图层，各个页面的 HTML 生成和交互

### 扩展开发
1. 添加新页面：在相应的 pages 文件中添加 HTML 生成函数
2. 添加新功能：在 data.js 中添加数据操作方法
3. 修改权限：在 auth.js 中配置角色权限

## 📄 许可证

本项目仅供学习和演示使用。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

---

**注意**: 本系统使用 LocalStorage 存储数据，仅适用于演示和开发环境。生产环境请使用专业的数据库系统。
