// 认证和权限管理模块
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.checkLoginStatus();
    }

    // 检查登录状态
    checkLoginStatus() {
        const username = localStorage.getItem('currentUser');
        if (username) {
            const user = dataManager.getUserByUsername(username);
            if (user) {
                this.currentUser = user;
                return true;
            } else {
                localStorage.removeItem('currentUser');
            }
        }
        return false;
    }

    // 登录
    login(username) {
        const user = dataManager.getUserByUsername(username);
        if (user) {
            this.currentUser = user;
            localStorage.setItem('currentUser', username);
            return { success: true, user: user };
        }
        return { success: false, message: '用户不存在' };
    }

    // 登出
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
    }

    // 获取当前用户
    getCurrentUser() {
        return this.currentUser;
    }

    // 检查是否已登录
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // 权限检查
    hasPermission(action) {
        if (!this.currentUser) return false;

        const permissions = this.getRolePermissions(this.currentUser.role);
        return permissions.includes(action) || permissions.includes('*');
    }

    // 获取角色权限
    getRolePermissions(role) {
        const rolePermissions = {
            '管理员': ['*'], // 管理员拥有所有权限
            '医生': [
                'view_dashboard',
                'view_units',
                'view_invoices',
                'issue_invoice',
                'view_archive',
                'view_reports'
            ],
            '财务': [
                'view_dashboard',
                'view_units',
                'view_invoices',
                'issue_invoice',
                'view_archive',
                'delivery_invoice',
                'view_reports',
                'manage_invoices'
            ]
        };

        return rolePermissions[role] || [];
    }

    // 检查页面访问权限
    canAccessPage(page) {
        if (!this.isLoggedIn()) return false;

        const pagePermissions = {
            'dashboard': 'view_dashboard',
            'units': 'view_units',
            'invoices': 'view_invoices',
            'issue': 'issue_invoice',
            'archive': 'view_archive',
            'delivery': 'delivery_invoice',
            'reports': 'view_reports',
            'users': 'manage_users',
            'miniapp': 'view_miniapp'
        };

        const requiredPermission = pagePermissions[page];
        return !requiredPermission || this.hasPermission(requiredPermission);
    }

    // 获取用户可访问的菜单项
    getAccessibleMenuItems() {
        const allMenuItems = [
            { id: 'dashboard', name: '仪表盘', icon: 'fas fa-tachometer-alt', permission: 'view_dashboard' },
            { id: 'units', name: '单位管理', icon: 'fas fa-building', permission: 'view_units' },
            { id: 'invoices', name: '票据管理', icon: 'fas fa-file-invoice', permission: 'view_invoices' },
            { id: 'issue', name: '开具票据', icon: 'fas fa-plus-circle', permission: 'issue_invoice' },
            { id: 'archive', name: '存档查询', icon: 'fas fa-archive', permission: 'view_archive' },
            { id: 'delivery', name: '票据交付', icon: 'fas fa-paper-plane', permission: 'delivery_invoice' },
            { id: 'reports', name: '统计报表', icon: 'fas fa-chart-bar', permission: 'view_reports' },
            { id: 'users', name: '用户管理', icon: 'fas fa-users', permission: 'manage_users' },
            { id: 'miniapp', name: '小程序取票', icon: 'fas fa-qrcode', permission: 'view_miniapp' }
        ];

        return allMenuItems.filter(item => 
            !item.permission || this.hasPermission(item.permission)
        );
    }
}

// 创建全局认证管理实例
const authManager = new AuthManager();

// 全局登录函数
function login() {
    const username = document.getElementById('username').value.trim();
    
    if (!username) {
        showMessage('请输入用户名', 'error');
        return;
    }

    const result = authManager.login(username);
    if (result.success) {
        showMessage(`欢迎，${result.user.name}！`, 'success');
        setTimeout(() => {
            showDashboard();
        }, 1000);
    } else {
        showMessage(result.message, 'error');
    }
}

// 全局登出函数
function logout() {
    authManager.logout();
    showLogin();
    showMessage('已安全退出系统', 'info');
}

// 显示登录页面
function showLogin() {
    document.getElementById('loginPage').classList.remove('hidden');
    document.getElementById('dashboardPage').classList.add('hidden');
    document.getElementById('username').value = '';
    document.getElementById('username').focus();
}

// 显示仪表盘
function showDashboard() {
    document.getElementById('loginPage').classList.add('hidden');
    document.getElementById('dashboardPage').classList.remove('hidden');
    
    // 更新当前用户显示
    const user = authManager.getCurrentUser();
    document.getElementById('currentUserDisplay').textContent = 
        `${user.name} (${user.role})`;
    
    // 更新导航菜单
    updateNavigationMenu();
    
    // 显示默认页面
    showPage('dashboard');
}

// 更新导航菜单
function updateNavigationMenu() {
    const menuItems = authManager.getAccessibleMenuItems();
    const navContainer = document.querySelector('nav.space-y-2');
    
    if (navContainer) {
        navContainer.innerHTML = menuItems.map(item => `
            <a href="#" onclick="showPage('${item.id}')" class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600">
                <i class="${item.icon} mr-3"></i>${item.name}
            </a>
        `).join('');
    }
}

// 权限检查装饰器
function requirePermission(permission) {
    return function(target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = function(...args) {
            if (!authManager.hasPermission(permission)) {
                showMessage('您没有权限执行此操作', 'error');
                return;
            }
            return originalMethod.apply(this, args);
        };
        return descriptor;
    };
}

// 页面访问权限检查
function checkPageAccess(page) {
    if (!authManager.canAccessPage(page)) {
        showMessage('您没有权限访问此页面', 'error');
        return false;
    }
    return true;
}
