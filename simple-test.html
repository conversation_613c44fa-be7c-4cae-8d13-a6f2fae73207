<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow p-6">
        <h1 class="text-2xl font-bold mb-6">系统功能测试</h1>
        
        <div id="testResults" class="space-y-4 mb-6">
            <p>正在加载JavaScript文件...</p>
        </div>
        
        <div class="space-y-4">
            <button onclick="testBasicFunctions()" class="w-full bg-blue-500 text-white p-3 rounded">
                测试基础函数
            </button>
            
            <button onclick="testLogin()" class="w-full bg-green-500 text-white p-3 rounded">
                测试登录功能
            </button>
            
            <button onclick="testDataOperations()" class="w-full bg-yellow-500 text-white p-3 rounded">
                测试数据操作
            </button>
        </div>
    </div>

    <!-- 按顺序加载JavaScript文件 -->
    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/pages.js"></script>
    <script src="js/pages-extended.js"></script>
    <script src="js/pages-final.js"></script>
    
    <script>
        function log(message, isError = false) {
            const div = document.getElementById('testResults');
            const p = document.createElement('p');
            p.className = isError ? 'text-red-600' : 'text-green-600';
            p.textContent = new Date().toLocaleTimeString() + ': ' + message;
            div.appendChild(p);
        }
        
        function testBasicFunctions() {
            log('开始测试基础函数...');
            
            // 测试页面函数
            const pageFunctions = [
                'getDashboardHTML',
                'getUnitsHTML', 
                'getInvoicesHTML',
                'getIssueHTML',
                'getArchiveHTML',
                'getDeliveryHTML',
                'getReportsHTML',
                'getUsersHTML',
                'getMiniAppHTML'
            ];
            
            let successCount = 0;
            pageFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    try {
                        const result = window[funcName]();
                        if (result && result.length > 0) {
                            log(`✓ ${funcName} 正常工作`);
                            successCount++;
                        } else {
                            log(`✗ ${funcName} 返回空内容`, true);
                        }
                    } catch (error) {
                        log(`✗ ${funcName} 执行错误: ${error.message}`, true);
                    }
                } else {
                    log(`✗ ${funcName} 函数不存在`, true);
                }
            });
            
            log(`基础函数测试完成: ${successCount}/${pageFunctions.length} 成功`);
        }
        
        function testLogin() {
            log('开始测试登录功能...');
            
            try {
                if (typeof authManager !== 'undefined') {
                    log('✓ authManager 已加载');
                    
                    const loginResult = authManager.login('admin');
                    if (loginResult.success) {
                        log('✓ 登录成功');
                        
                        const hasPermission = authManager.hasPermission('*');
                        if (hasPermission) {
                            log('✓ 权限检查正常');
                        } else {
                            log('✗ 权限检查失败', true);
                        }
                        
                        authManager.logout();
                        log('✓ 登出成功');
                    } else {
                        log('✗ 登录失败', true);
                    }
                } else {
                    log('✗ authManager 未加载', true);
                }
            } catch (error) {
                log(`✗ 登录测试错误: ${error.message}`, true);
            }
        }
        
        function testDataOperations() {
            log('开始测试数据操作...');
            
            try {
                if (typeof dataManager !== 'undefined') {
                    log('✓ dataManager 已加载');
                    
                    // 测试获取数据
                    const units = dataManager.getUnits();
                    log(`✓ 获取到 ${units.length} 个单位`);
                    
                    const invoices = dataManager.getInvoices();
                    log(`✓ 获取到 ${invoices.length} 张票据`);
                    
                    const users = dataManager.getUsers();
                    log(`✓ 获取到 ${users.length} 个用户`);
                    
                    // 测试统计
                    const stats = dataManager.getStatistics();
                    log(`✓ 统计数据: 总票据 ${stats.totalInvoices}, 总金额 ¥${stats.totalAmount.toFixed(2)}`);
                    
                } else {
                    log('✗ dataManager 未加载', true);
                }
            } catch (error) {
                log(`✗ 数据操作测试错误: ${error.message}`, true);
            }
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('所有脚本已加载完成');
                
                // 检查关键对象
                if (typeof dataManager !== 'undefined') {
                    log('✓ dataManager 可用');
                } else {
                    log('✗ dataManager 不可用', true);
                }
                
                if (typeof authManager !== 'undefined') {
                    log('✓ authManager 可用');
                } else {
                    log('✗ authManager 不可用', true);
                }
                
                if (typeof getDashboardHTML === 'function') {
                    log('✓ 页面函数可用');
                } else {
                    log('✗ 页面函数不可用', true);
                }
                
            }, 500);
        });
    </script>
</body>
</html>
