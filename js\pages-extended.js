// 扩展页面内容 - 票据开具、存档、交付等模块

// 票据开具页面
function getIssueHTML() {
  return `
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-2xl font-bold text-gray-800">开具票据</h2>
                <p class="text-gray-600 mt-2">请填写患者信息和就诊详情</p>
            </div>
            
            <div class="p-6">
                <form id="issueForm" class="space-y-6">
                    <!-- 患者信息 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-4">患者信息</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">患者姓名 *</label>
                                <input type="text" id="patientName" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">身份证号</label>
                                <input type="text" id="patientId" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       pattern="[0-9X]{18}" placeholder="18位身份证号">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">联系电话 *</label>
                                <input type="tel" id="patientPhone" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       pattern="[0-9]{11}" placeholder="11位手机号">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">年龄</label>
                                <input type="number" id="patientAge" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       min="0" max="150">
                            </div>
                        </div>
                    </div>

                    <!-- 就诊信息 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-4">就诊信息</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">票据类型 *</label>
                                <select id="invoiceType" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择票据类型</option>
                                    <option value="门诊">门诊</option>
                                    <option value="住院">住院</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">科室 *</label>
                                <select id="department" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择科室</option>
                                    <option value="内科">内科</option>
                                    <option value="外科">外科</option>
                                    <option value="儿科">儿科</option>
                                    <option value="妇科">妇科</option>
                                    <option value="骨科">骨科</option>
                                    <option value="眼科">眼科</option>
                                    <option value="耳鼻喉科">耳鼻喉科</option>
                                    <option value="皮肤科">皮肤科</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">医生 *</label>
                                <input type="text" id="doctor" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">单位</label>
                                <select id="unitId" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择单位</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 费用信息 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-4">费用信息</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">总金额 *</label>
                                <input type="number" id="amount" required step="0.01" min="0" 
                                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">支付方式</label>
                                <select id="paymentMethod" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="现金">现金</option>
                                    <option value="银行卡">银行卡</option>
                                    <option value="支付宝">支付宝</option>
                                    <option value="微信支付">微信支付</option>
                                    <option value="医保">医保</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">费用明细</label>
                            <textarea id="feeDetails" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                                      placeholder="请输入费用明细（可选）"></textarea>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="resetIssueForm()" class="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                            <i class="fas fa-refresh mr-2"></i>重置
                        </button>
                        <button type="submit" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <i class="fas fa-plus-circle mr-2"></i>开具票据
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `
}

function initIssueForm() {
  // 加载单位选项
  const units = dataManager.getUnits()
  const unitSelect = document.getElementById('unitId')
  unitSelect.innerHTML =
    '<option value="">请选择单位</option>' +
    units.map(unit => `<option value="${unit.id}">${unit.name}</option>`).join('')

  // 绑定表单提交事件
  document.getElementById('issueForm').addEventListener('submit', function (e) {
    e.preventDefault()
    issueInvoice()
  })

  // 自动填充当前医生
  const currentUser = authManager.getCurrentUser()
  if (currentUser && currentUser.role === '医生') {
    document.getElementById('doctor').value = currentUser.name
  }
}

function issueInvoice() {
  const formData = {
    patientName: document.getElementById('patientName').value.trim(),
    patientId: document.getElementById('patientId').value.trim(),
    phone: document.getElementById('patientPhone').value.trim(),
    age: document.getElementById('patientAge').value,
    type: document.getElementById('invoiceType').value,
    department: document.getElementById('department').value,
    doctor: document.getElementById('doctor').value.trim(),
    unitId: document.getElementById('unitId').value,
    amount: parseFloat(document.getElementById('amount').value),
    paymentMethod: document.getElementById('paymentMethod').value,
    feeDetails: document.getElementById('feeDetails').value.trim(),
  }

  // 验证必填字段
  if (
    !formData.patientName ||
    !formData.phone ||
    !formData.type ||
    !formData.department ||
    !formData.doctor ||
    !formData.amount
  ) {
    showMessage('请填写所有必填字段', 'error')
    return
  }

  // 验证手机号格式
  if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
    showMessage('请输入正确的手机号格式', 'error')
    return
  }

  // 验证金额
  if (formData.amount <= 0) {
    showMessage('金额必须大于0', 'error')
    return
  }

  try {
    const invoice = dataManager.addInvoice(formData)
    showMessage(`票据开具成功！票据号：${invoice.id}`, 'success')

    // 询问是否打印
    setTimeout(() => {
      showConfirm('票据开具成功，是否立即打印？', () => {
        printInvoice(invoice.id)
      })
    }, 1000)

    // 重置表单
    resetIssueForm()
  } catch (error) {
    showMessage('开具失败：' + error.message, 'error')
  }
}

function resetIssueForm() {
  document.getElementById('issueForm').reset()
  const currentUser = authManager.getCurrentUser()
  if (currentUser && currentUser.role === '医生') {
    document.getElementById('doctor').value = currentUser.name
  }
}

// 存档查询页面
function getArchiveHTML() {
  return `
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-800">存档票据查询</h2>
                    <button onclick="archiveAllInvoices()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-archive mr-2"></i>批量存档
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                <!-- 筛选栏 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <input type="text" id="archiveSearch" placeholder="搜索患者姓名或票据号..." 
                           class="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                           onkeyup="filterArchiveInvoices()">
                    
                    <input type="date" id="archiveDateFrom" class="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                           onchange="filterArchiveInvoices()">
                    
                    <input type="date" id="archiveDateTo" class="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" 
                           onchange="filterArchiveInvoices()">
                </div>
                
                <!-- 存档票据列表 -->
                <div class="overflow-x-auto">
                    <table id="archiveTable" class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-left">票据号</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">患者姓名</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">类型</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">金额</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">开具时间</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">存档时间</th>
                                <th class="border border-gray-300 px-4 py-3 text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody id="archiveTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div id="archivePagination" class="mt-4"></div>
            </div>
        </div>
    `
}

let archivePagination = null

function loadArchiveData() {
  const invoices = dataManager.getInvoices().filter(invoice => invoice.archived)
  archivePagination = new Pagination(invoices, 10)
  renderArchiveTable()
}

function renderArchiveTable() {
  const currentData = archivePagination.getCurrentPageData()
  const tbody = document.getElementById('archiveTableBody')

  tbody.innerHTML = currentData
    .map(
      invoice => `
        <tr class="hover:bg-gray-50">
            <td class="border border-gray-300 px-4 py-3 font-mono">${invoice.id}</td>
            <td class="border border-gray-300 px-4 py-3 font-medium">${invoice.patientName}</td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="px-2 py-1 rounded-full text-xs ${
                  invoice.type === '门诊'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-green-100 text-green-800'
                }">
                    ${invoice.type}
                </span>
            </td>
            <td class="border border-gray-300 px-4 py-3 font-medium">${formatAmount(
              invoice.amount
            )}</td>
            <td class="border border-gray-300 px-4 py-3">${formatDate(invoice.createTime)}</td>
            <td class="border border-gray-300 px-4 py-3">${formatDate(
              invoice.archiveTime || invoice.createTime
            )}</td>
            <td class="border border-gray-300 px-4 py-3 text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="viewInvoice('${
                      invoice.id
                    }')" class="text-blue-600 hover:text-blue-800" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="downloadArchiveInvoice('${
                      invoice.id
                    }')" class="text-green-600 hover:text-green-800" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </td>
        </tr>
    `
    )
    .join('')

  // 更新分页
  document.getElementById('archivePagination').innerHTML =
    archivePagination.getPaginationHTML('goToArchivePage')
}

function goToArchivePage(page) {
  if (archivePagination.goToPage(page)) {
    renderArchiveTable()
  }
}

function filterArchiveInvoices() {
  const searchTerm = document.getElementById('archiveSearch').value.toLowerCase()
  const dateFrom = document.getElementById('archiveDateFrom').value
  const dateTo = document.getElementById('archiveDateTo').value

  let filteredInvoices = dataManager.getInvoices().filter(invoice => invoice.archived)

  if (searchTerm) {
    filteredInvoices = filteredInvoices.filter(
      invoice =>
        invoice.patientName.toLowerCase().includes(searchTerm) ||
        invoice.id.toLowerCase().includes(searchTerm)
    )
  }

  if (dateFrom) {
    filteredInvoices = filteredInvoices.filter(
      invoice => new Date(invoice.createTime) >= new Date(dateFrom)
    )
  }

  if (dateTo) {
    filteredInvoices = filteredInvoices.filter(
      invoice => new Date(invoice.createTime) <= new Date(dateTo + 'T23:59:59')
    )
  }

  archivePagination = new Pagination(filteredInvoices, 10)
  renderArchiveTable()
}

function archiveAllInvoices() {
  const unArchivedInvoices = dataManager.getInvoices().filter(invoice => !invoice.archived)

  if (unArchivedInvoices.length === 0) {
    showMessage('没有需要存档的票据', 'info')
    return
  }

  showConfirm(`确定要存档 ${unArchivedInvoices.length} 张票据吗？`, () => {
    try {
      unArchivedInvoices.forEach(invoice => {
        dataManager.updateInvoice(invoice.id, {
          archived: true,
          archiveTime: new Date().toISOString(),
        })
      })

      showMessage(`成功存档 ${unArchivedInvoices.length} 张票据`, 'success')
      loadArchiveData()
    } catch (error) {
      showMessage('存档失败：' + error.message, 'error')
    }
  })
}

function downloadArchiveInvoice(id) {
  const invoice = dataManager.getById('invoices', id)
  if (!invoice) {
    showMessage('票据不存在', 'error')
    return
  }

  exportData([invoice], `archive_invoice_${invoice.id}.json`)
  showMessage('票据下载成功', 'success')
}

// 票据交付页面
function getDeliveryHTML() {
  return `
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-2xl font-bold text-gray-800">票据交付</h2>
                <p class="text-gray-600 mt-2">选择票据并配置交付方式</p>
            </div>

            <div class="p-6">
                <!-- 票据选择 -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-4">选择票据</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <input type="text" id="deliverySearch" placeholder="搜索患者姓名或票据号..."
                               class="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                               onkeyup="filterDeliveryInvoices()">
                        <select id="deliveryStatusFilter" class="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" onchange="filterDeliveryInvoices()">
                            <option value="">所有状态</option>
                            <option value="未交付">未交付</option>
                            <option value="已交付">已交付</option>
                        </select>
                    </div>

                    <div class="overflow-x-auto max-h-64 border border-gray-300 rounded-lg">
                        <table class="w-full">
                            <thead class="bg-gray-100 sticky top-0">
                                <tr>
                                    <th class="px-4 py-3 text-left">选择</th>
                                    <th class="px-4 py-3 text-left">票据号</th>
                                    <th class="px-4 py-3 text-left">患者姓名</th>
                                    <th class="px-4 py-3 text-left">金额</th>
                                    <th class="px-4 py-3 text-left">状态</th>
                                </tr>
                            </thead>
                            <tbody id="deliveryInvoicesBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 交付配置 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-4">交付配置</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">交付方式 *</label>
                            <select id="deliveryMethod" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择交付方式</option>
                                <option value="短信">短信</option>
                                <option value="邮件">邮件</option>
                                <option value="小程序">小程序</option>
                                <option value="APP推送">APP推送</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">接收方式</label>
                            <input type="text" id="deliveryTarget" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                   placeholder="手机号/邮箱地址">
                        </div>
                    </div>

                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">交付消息模板</label>
                        <textarea id="deliveryMessage" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                  placeholder="您的医疗票据已准备就绪，请点击链接查看..."></textarea>
                    </div>

                    <div class="flex justify-end space-x-4 mt-6">
                        <button onclick="previewDelivery()" class="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600">
                            <i class="fas fa-eye mr-2"></i>预览
                        </button>
                        <button onclick="executeDelivery()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <i class="fas fa-paper-plane mr-2"></i>执行交付
                        </button>
                    </div>
                </div>

                <!-- 交付记录 -->
                <div class="mt-8">
                    <h3 class="text-lg font-semibold mb-4">交付记录</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-300">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="border border-gray-300 px-4 py-3 text-left">票据号</th>
                                    <th class="border border-gray-300 px-4 py-3 text-left">患者姓名</th>
                                    <th class="border border-gray-300 px-4 py-3 text-left">交付方式</th>
                                    <th class="border border-gray-300 px-4 py-3 text-left">接收方</th>
                                    <th class="border border-gray-300 px-4 py-3 text-left">交付时间</th>
                                    <th class="border border-gray-300 px-4 py-3 text-left">状态</th>
                                </tr>
                            </thead>
                            <tbody id="deliveryRecordsBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `
}

function loadDeliveryData() {
  loadDeliveryInvoices()
  loadDeliveryRecords()
}

function loadDeliveryInvoices() {
  const invoices = dataManager.getInvoices().filter(invoice => invoice.status === '已开具')
  const tbody = document.getElementById('deliveryInvoicesBody')

  tbody.innerHTML = invoices
    .map(
      invoice => `
        <tr class="hover:bg-gray-50">
            <td class="px-4 py-3">
                <input type="checkbox" value="${invoice.id}" class="delivery-invoice-checkbox">
            </td>
            <td class="px-4 py-3 font-mono text-sm">${invoice.id}</td>
            <td class="px-4 py-3 font-medium">${invoice.patientName}</td>
            <td class="px-4 py-3">${formatAmount(invoice.amount)}</td>
            <td class="px-4 py-3">
                <span class="px-2 py-1 rounded-full text-xs ${
                  invoice.delivered
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }">
                    ${invoice.delivered ? '已交付' : '未交付'}
                </span>
            </td>
        </tr>
    `
    )
    .join('')
}

function loadDeliveryRecords() {
  const deliveries = dataManager.getDeliveries()
  const tbody = document.getElementById('deliveryRecordsBody')

  tbody.innerHTML = deliveries
    .map(
      delivery => `
        <tr class="hover:bg-gray-50">
            <td class="border border-gray-300 px-4 py-3 font-mono text-sm">${
              delivery.invoiceId
            }</td>
            <td class="border border-gray-300 px-4 py-3">${delivery.patientName}</td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                    ${delivery.method}
                </span>
            </td>
            <td class="border border-gray-300 px-4 py-3">${delivery.target}</td>
            <td class="border border-gray-300 px-4 py-3">${formatDate(delivery.createTime)}</td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                    成功
                </span>
            </td>
        </tr>
    `
    )
    .join('')
}

function filterDeliveryInvoices() {
  const searchTerm = document.getElementById('deliverySearch').value.toLowerCase()
  const statusFilter = document.getElementById('deliveryStatusFilter').value

  let filteredInvoices = dataManager.getInvoices().filter(invoice => invoice.status === '已开具')

  if (searchTerm) {
    filteredInvoices = filteredInvoices.filter(
      invoice =>
        invoice.patientName.toLowerCase().includes(searchTerm) ||
        invoice.id.toLowerCase().includes(searchTerm)
    )
  }

  if (statusFilter) {
    if (statusFilter === '已交付') {
      filteredInvoices = filteredInvoices.filter(invoice => invoice.delivered)
    } else if (statusFilter === '未交付') {
      filteredInvoices = filteredInvoices.filter(invoice => !invoice.delivered)
    }
  }

  const tbody = document.getElementById('deliveryInvoicesBody')
  tbody.innerHTML = filteredInvoices
    .map(
      invoice => `
        <tr class="hover:bg-gray-50">
            <td class="px-4 py-3">
                <input type="checkbox" value="${invoice.id}" class="delivery-invoice-checkbox">
            </td>
            <td class="px-4 py-3 font-mono text-sm">${invoice.id}</td>
            <td class="px-4 py-3 font-medium">${invoice.patientName}</td>
            <td class="px-4 py-3">${formatAmount(invoice.amount)}</td>
            <td class="px-4 py-3">
                <span class="px-2 py-1 rounded-full text-xs ${
                  invoice.delivered
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }">
                    ${invoice.delivered ? '已交付' : '未交付'}
                </span>
            </td>
        </tr>
    `
    )
    .join('')
}

function previewDelivery() {
  const selectedInvoices = getSelectedInvoices()
  const method = document.getElementById('deliveryMethod').value
  const target = document.getElementById('deliveryTarget').value
  const message = document.getElementById('deliveryMessage').value

  if (selectedInvoices.length === 0) {
    showMessage('请选择要交付的票据', 'error')
    return
  }

  if (!method) {
    showMessage('请选择交付方式', 'error')
    return
  }

  const content = `
        <div class="space-y-4">
            <div>
                <h4 class="font-semibold">交付预览</h4>
                <p class="text-sm text-gray-600">将要交付 ${selectedInvoices.length} 张票据</p>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg">
                <p><strong>交付方式：</strong>${method}</p>
                <p><strong>接收方：</strong>${target || '使用票据中的联系方式'}</p>
                <p><strong>消息内容：</strong></p>
                <div class="mt-2 p-3 bg-white border rounded">
                    ${message || '您的医疗票据已准备就绪，请点击链接查看详情。'}
                </div>
            </div>

            <div>
                <h5 class="font-medium mb-2">票据列表：</h5>
                <ul class="text-sm space-y-1">
                    ${selectedInvoices
                      .map(
                        invoice => `
                        <li>• ${invoice.id} - ${invoice.patientName} - ${formatAmount(
                          invoice.amount
                        )}</li>
                    `
                      )
                      .join('')}
                </ul>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
                <button onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    关闭
                </button>
            </div>
        </div>
    `

  showModal('交付预览', content)
}

function executeDelivery() {
  const selectedInvoices = getSelectedInvoices()
  const method = document.getElementById('deliveryMethod').value
  const target = document.getElementById('deliveryTarget').value
  const message = document.getElementById('deliveryMessage').value

  if (selectedInvoices.length === 0) {
    showMessage('请选择要交付的票据', 'error')
    return
  }

  if (!method) {
    showMessage('请选择交付方式', 'error')
    return
  }

  showConfirm(`确定要交付 ${selectedInvoices.length} 张票据吗？`, () => {
    try {
      selectedInvoices.forEach(invoice => {
        // 创建交付记录
        const delivery = {
          invoiceId: invoice.id,
          patientName: invoice.patientName,
          method: method,
          target: target || invoice.phone,
          message: message || '您的医疗票据已准备就绪，请点击链接查看详情。',
          status: '成功'
        };

        dataManager.addDelivery(delivery);

        // 更新票据状态
        dataManager.updateInvoice(invoice.id, { delivered: true });
      });

      showMessage(`成功交付 ${selectedInvoices.length} 张票据`, 'success');

      // 清空选择和表单
      document.querySelectorAll('.delivery-invoice-checkbox').forEach(cb => cb.checked = false);
      document.getElementById('deliveryMethod').value = '';
      document.getElementById('deliveryTarget').value = '';
      document.getElementById('deliveryMessage').value = '';

      // 重新加载数据
      loadDeliveryData();

    } catch (error) {
      showMessage('交付失败：' + error.message, 'error');
    }
  });
}

function getSelectedInvoices() {
  const checkboxes = document.querySelectorAll('.delivery-invoice-checkbox:checked');
  const selectedIds = Array.from(checkboxes).map(cb => cb.value);
  return selectedIds.map(id => dataManager.getById('invoices', id)).filter(Boolean);
}
          patientName: invoice.patientName,
          method: method,
          target: target || invoice.phone,
          message: message || '您的医疗票据已准备就绪，请点击链接查看详情。',
          status: '成功',
        }

        dataManager.addDelivery(delivery)

        // 更新票据状态
        dataManager.updateInvoice(invoice.id, { delivered: true })
      })

      showMessage(`成功交付 ${selectedInvoices.length} 张票据`, 'success')

      // 清空选择和表单
      document.querySelectorAll('.delivery-invoice-checkbox').forEach(cb => (cb.checked = false))
      document.getElementById('deliveryMethod').value = ''
      document.getElementById('deliveryTarget').value = ''
      document.getElementById('deliveryMessage').value = ''

      // 重新加载数据
      loadDeliveryData()
    } catch (error) {
      showMessage('交付失败：' + error.message, 'error')
    }
  })
}

function getSelectedInvoices() {
  const checkboxes = document.querySelectorAll('.delivery-invoice-checkbox:checked')
  const selectedIds = Array.from(checkboxes).map(cb => cb.value)
  return selectedIds.map(id => dataManager.getById('invoices', id)).filter(Boolean)
}
