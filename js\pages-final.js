// 最终页面模块 - 报表、用户管理、小程序

// 统计报表页面
function getReportsHTML() {
  return `
        <div class="space-y-6">
            <!-- 概览统计 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">统计报表</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100">本月开具</p>
                                <p id="monthlyInvoices" class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-calendar-alt text-4xl text-blue-200"></i>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100">本月收入</p>
                                <p id="monthlyRevenue" class="text-3xl font-bold">¥0</p>
                            </div>
                            <i class="fas fa-money-bill-wave text-4xl text-green-200"></i>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-100">交付率</p>
                                <p id="deliveryRate" class="text-3xl font-bold">0%</p>
                            </div>
                            <i class="fas fa-percentage text-4xl text-yellow-200"></i>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100">冲红率</p>
                                <p id="refundRate" class="text-3xl font-bold">0%</p>
                            </div>
                            <i class="fas fa-undo text-4xl text-purple-200"></i>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-4">科室收入分布</h3>
                        <canvas id="departmentChart" width="400" height="300"></canvas>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-4">交付方式统计</h3>
                        <canvas id="deliveryMethodChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- 详细报表 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold">详细数据</h3>
                    <div class="flex space-x-2">
                        <button onclick="exportReport()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                            <i class="fas fa-download mr-2"></i>导出报表
                        </button>
                        <button onclick="printReport()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            <i class="fas fa-print mr-2"></i>打印报表
                        </button>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-left">科室</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">开具数量</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">总金额</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">交付数量</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">冲红数量</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">交付率</th>
                            </tr>
                        </thead>
                        <tbody id="reportTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `
}

function loadReportsData() {
  const invoices = dataManager.getInvoices()
  const deliveries = dataManager.getDeliveries()

  // 计算统计数据
  const currentMonth = new Date().getMonth()
  const currentYear = new Date().getFullYear()

  const monthlyInvoices = invoices.filter(inv => {
    const invDate = new Date(inv.createTime)
    return invDate.getMonth() === currentMonth && invDate.getFullYear() === currentYear
  })

  const monthlyRevenue = monthlyInvoices.reduce((sum, inv) => sum + inv.amount, 0)
  const deliveredCount = invoices.filter(inv => inv.delivered).length
  const refundedCount = invoices.filter(inv => inv.status === '已冲红').length

  const deliveryRate =
    invoices.length > 0 ? ((deliveredCount / invoices.length) * 100).toFixed(1) : 0
  const refundRate = invoices.length > 0 ? ((refundedCount / invoices.length) * 100).toFixed(1) : 0

  // 更新统计卡片
  document.getElementById('monthlyInvoices').textContent = monthlyInvoices.length
  document.getElementById('monthlyRevenue').textContent = formatAmount(monthlyRevenue)
  document.getElementById('deliveryRate').textContent = deliveryRate + '%'
  document.getElementById('refundRate').textContent = refundRate + '%'

  // 加载图表
  loadReportCharts(invoices, deliveries)

  // 加载详细报表
  loadDetailedReport(invoices)
}

function loadReportCharts(invoices, deliveries) {
  // 科室收入分布
  const departmentData = {}
  invoices.forEach(invoice => {
    const dept = invoice.department || '未分类'
    departmentData[dept] = (departmentData[dept] || 0) + invoice.amount
  })

  new Chart(document.getElementById('departmentChart'), {
    type: 'bar',
    data: {
      labels: Object.keys(departmentData),
      datasets: [
        {
          label: '收入金额',
          data: Object.values(departmentData),
          backgroundColor: 'rgba(59, 130, 246, 0.8)',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 1,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function (value) {
              return '¥' + value.toFixed(0)
            },
          },
        },
      },
    },
  })

  // 交付方式统计
  const methodData = {}
  deliveries.forEach(delivery => {
    methodData[delivery.method] = (methodData[delivery.method] || 0) + 1
  })

  new Chart(document.getElementById('deliveryMethodChart'), {
    type: 'pie',
    data: {
      labels: Object.keys(methodData),
      datasets: [
        {
          data: Object.values(methodData),
          backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'],
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
    },
  })
}

function loadDetailedReport(invoices) {
  const departmentStats = {}

  invoices.forEach(invoice => {
    const dept = invoice.department || '未分类'
    if (!departmentStats[dept]) {
      departmentStats[dept] = {
        count: 0,
        amount: 0,
        delivered: 0,
        refunded: 0,
      }
    }

    departmentStats[dept].count++
    departmentStats[dept].amount += invoice.amount
    if (invoice.delivered) departmentStats[dept].delivered++
    if (invoice.status === '已冲红') departmentStats[dept].refunded++
  })

  const tbody = document.getElementById('reportTableBody')
  tbody.innerHTML = Object.entries(departmentStats)
    .map(([dept, stats]) => {
      const deliveryRate = stats.count > 0 ? ((stats.delivered / stats.count) * 100).toFixed(1) : 0
      return `
            <tr class="hover:bg-gray-50">
                <td class="border border-gray-300 px-4 py-3 font-medium">${dept}</td>
                <td class="border border-gray-300 px-4 py-3">${stats.count}</td>
                <td class="border border-gray-300 px-4 py-3 font-medium">${formatAmount(
                  stats.amount
                )}</td>
                <td class="border border-gray-300 px-4 py-3">${stats.delivered}</td>
                <td class="border border-gray-300 px-4 py-3">${stats.refunded}</td>
                <td class="border border-gray-300 px-4 py-3">${deliveryRate}%</td>
            </tr>
        `
    })
    .join('')
}

function exportReport() {
  const reportData = {
    exportTime: new Date().toISOString(),
    statistics: dataManager.getStatistics(),
    invoices: dataManager.getInvoices(),
    deliveries: dataManager.getDeliveries(),
  }

  exportData(
    reportData,
    'medical_invoice_report_' + new Date().toISOString().slice(0, 10) + '.json'
  )
  showMessage('报表导出成功', 'success')
}

function printReport() {
  printContent('reportTableBody')
}

// 用户管理页面
function getUsersHTML() {
  return `
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-800">用户管理</h2>
                    <button onclick="showAddUserModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        <i class="fas fa-plus mr-2"></i>添加用户
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                <!-- 搜索栏 -->
                <div class="mb-4">
                    <input type="text" id="userSearch" placeholder="搜索用户名或姓名..." 
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           onkeyup="searchTable('userSearch', 'usersTable')">
                </div>
                
                <!-- 用户列表 -->
                <div class="overflow-x-auto">
                    <table id="usersTable" class="w-full border-collapse border border-gray-300">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border border-gray-300 px-4 py-3 text-left">用户名</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">姓名</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">角色</th>
                                <th class="border border-gray-300 px-4 py-3 text-left">创建时间</th>
                                <th class="border border-gray-300 px-4 py-3 text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `
}

function loadUsersData() {
  const users = dataManager.getUsers()
  const tbody = document.getElementById('usersTableBody')

  tbody.innerHTML = users
    .map(
      user => `
        <tr class="hover:bg-gray-50">
            <td class="border border-gray-300 px-4 py-3 font-medium">${user.username}</td>
            <td class="border border-gray-300 px-4 py-3">${user.name || '-'}</td>
            <td class="border border-gray-300 px-4 py-3">
                <span class="px-2 py-1 rounded-full text-xs ${getRoleClass(user.role)}">
                    ${user.role}
                </span>
            </td>
            <td class="border border-gray-300 px-4 py-3">${formatDate(user.createTime)}</td>
            <td class="border border-gray-300 px-4 py-3 text-center">
                <button onclick="editUser('${
                  user.username
                }')" class="text-blue-600 hover:text-blue-800 mr-2">
                    <i class="fas fa-edit"></i>
                </button>
                ${
                  user.username !== 'admin'
                    ? `
                    <button onclick="deleteUser('${user.username}')" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-trash"></i>
                    </button>
                `
                    : ''
                }
            </td>
        </tr>
    `
    )
    .join('')
}

function getRoleClass(role) {
  const classes = {
    管理员: 'bg-red-100 text-red-800',
    医生: 'bg-blue-100 text-blue-800',
    财务: 'bg-green-100 text-green-800',
  }
  return classes[role] || 'bg-gray-100 text-gray-800'
}

function showAddUserModal() {
  const content = `
        <form id="userForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">用户名 *</label>
                <input type="text" id="newUsername" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">姓名 *</label>
                <input type="text" id="newUserName" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">角色 *</label>
                <select id="newUserRole" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="">请选择角色</option>
                    <option value="管理员">管理员</option>
                    <option value="医生">医生</option>
                    <option value="财务">财务</option>
                </select>
            </div>
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    取消
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    保存
                </button>
            </div>
        </form>
    `

  showModal('添加用户', content)

  document.getElementById('userForm').addEventListener('submit', function (e) {
    e.preventDefault()
    saveUser()
  })
}

function saveUser(editUsername = null) {
  const userData = {
    username: document.getElementById('newUsername').value.trim(),
    name: document.getElementById('newUserName').value.trim(),
    role: document.getElementById('newUserRole').value,
  }

  if (!userData.username || !userData.name || !userData.role) {
    showMessage('请填写所有必填字段', 'error')
    return
  }

  try {
    if (editUsername) {
      dataManager.updateUser(editUsername, userData)
      showMessage('用户信息更新成功', 'success')
    } else {
      // 检查用户名是否已存在
      if (dataManager.getUserByUsername(userData.username)) {
        showMessage('用户名已存在', 'error')
        return
      }
      dataManager.addUser(userData)
      showMessage('用户添加成功', 'success')
    }

    closeModal()
    loadUsersData()
  } catch (error) {
    showMessage('操作失败：' + error.message, 'error')
  }
}

function editUser(username) {
  const user = dataManager.getUserByUsername(username)
  if (!user) {
    showMessage('用户不存在', 'error')
    return
  }

  const content = `
        <form id="userForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">用户名 *</label>
                <input type="text" id="newUsername" value="${
                  user.username
                }" readonly class="w-full p-3 border border-gray-300 rounded-lg bg-gray-100">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">姓名 *</label>
                <input type="text" id="newUserName" value="${
                  user.name || ''
                }" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">角色 *</label>
                <select id="newUserRole" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="">请选择角色</option>
                    <option value="管理员" ${
                      user.role === '管理员' ? 'selected' : ''
                    }>管理员</option>
                    <option value="医生" ${user.role === '医生' ? 'selected' : ''}>医生</option>
                    <option value="财务" ${user.role === '财务' ? 'selected' : ''}>财务</option>
                </select>
            </div>
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    取消
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    更新
                </button>
            </div>
        </form>
    `

  showModal('编辑用户', content)

  document.getElementById('userForm').addEventListener('submit', function (e) {
    e.preventDefault()
    saveUser(username)
  })
}

function deleteUser(username) {
  showConfirm('确定要删除这个用户吗？此操作不可撤销。', () => {
    try {
      dataManager.deleteUser(username)
      showMessage('用户删除成功', 'success')
      loadUsersData()
    } catch (error) {
      showMessage('删除失败：' + error.message, 'error')
    }
  })
}

// 小程序取票页面
function getMiniAppHTML() {
  return `
        <div class="max-w-4xl mx-auto space-y-6">
            <!-- 小程序模拟器 -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-2xl font-bold text-gray-800">小程序取票模拟</h2>
                    <p class="text-gray-600 mt-2">模拟微信小程序扫码取票功能</p>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- 手机模拟器 -->
                        <div class="bg-gray-900 rounded-3xl p-4 mx-auto" style="width: 300px; height: 600px;">
                            <div class="bg-white rounded-2xl h-full p-4 overflow-y-auto">
                                <div class="text-center mb-6">
                                    <div class="w-12 h-12 bg-blue-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                                        <i class="fas fa-hospital text-white text-xl"></i>
                                    </div>
                                    <h3 class="font-bold text-lg">医疗票据小程序</h3>
                                    <p class="text-gray-600 text-sm">扫码或输入手机号查看票据</p>
                                </div>

                                <!-- 查询方式选择 -->
                                <div class="mb-4">
                                    <div class="flex border rounded-lg overflow-hidden">
                                        <button id="phoneTabBtn" onclick="switchMiniAppTab('phone')"
                                                class="flex-1 py-2 px-4 text-sm bg-blue-500 text-white">
                                            手机号查询
                                        </button>
                                        <button id="qrTabBtn" onclick="switchMiniAppTab('qr')"
                                                class="flex-1 py-2 px-4 text-sm bg-gray-200 text-gray-700">
                                            扫码查询
                                        </button>
                                    </div>
                                </div>

                                <!-- 手机号查询 -->
                                <div id="phoneQueryTab" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                                        <input type="tel" id="miniAppPhone" placeholder="请输入手机号"
                                               class="w-full p-3 border border-gray-300 rounded-lg text-sm">
                                    </div>
                                    <button onclick="queryByPhone()" class="w-full bg-blue-500 text-white py-3 rounded-lg text-sm">
                                        查询票据
                                    </button>
                                </div>

                                <!-- 扫码查询 -->
                                <div id="qrQueryTab" class="hidden space-y-4">
                                    <div class="text-center">
                                        <div class="w-48 h-48 border-2 border-dashed border-gray-300 rounded-lg mx-auto flex items-center justify-center mb-4">
                                            <div class="text-center">
                                                <i class="fas fa-qrcode text-4xl text-gray-400 mb-2"></i>
                                                <p class="text-sm text-gray-600">将二维码对准此区域</p>
                                            </div>
                                        </div>
                                        <button onclick="simulateQRScan()" class="bg-green-500 text-white py-2 px-4 rounded-lg text-sm">
                                            模拟扫码
                                        </button>
                                    </div>
                                </div>

                                <!-- 查询结果 -->
                                <div id="miniAppResults" class="mt-6">
                                    <!-- 结果将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        </div>

                        <!-- 二维码生成器 -->
                        <div class="space-y-6">
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h3 class="text-lg font-semibold mb-4">二维码生成器</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">选择票据</label>
                                        <select id="qrInvoiceSelect" class="w-full p-3 border border-gray-300 rounded-lg">
                                            <option value="">请选择票据</option>
                                        </select>
                                    </div>
                                    <button onclick="generateQRCode()" class="w-full bg-blue-600 text-white py-3 rounded-lg">
                                        生成二维码
                                    </button>
                                </div>

                                <div id="qrCodeContainer" class="mt-6 text-center">
                                    <!-- 二维码将在这里显示 -->
                                </div>
                            </div>

                            <!-- 使用说明 -->
                            <div class="bg-blue-50 rounded-lg p-6">
                                <h3 class="text-lg font-semibold mb-4 text-blue-800">使用说明</h3>
                                <ul class="text-sm text-blue-700 space-y-2">
                                    <li>• 患者可通过手机号查询已交付的票据</li>
                                    <li>• 扫描二维码可直接查看特定票据</li>
                                    <li>• 支持票据详情查看和下载</li>
                                    <li>• 模拟微信小程序的用户体验</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
}

function initMiniApp() {
  // 加载票据选项
  const invoices = dataManager.getInvoices().filter(inv => inv.delivered)
  const select = document.getElementById('qrInvoiceSelect')
  select.innerHTML =
    '<option value="">请选择票据</option>' +
    invoices
      .map(inv => `<option value="${inv.id}">${inv.id} - ${inv.patientName}</option>`)
      .join('')
}

function switchMiniAppTab(tab) {
  // 切换标签样式
  document.getElementById('phoneTabBtn').className =
    tab === 'phone'
      ? 'flex-1 py-2 px-4 text-sm bg-blue-500 text-white'
      : 'flex-1 py-2 px-4 text-sm bg-gray-200 text-gray-700'

  document.getElementById('qrTabBtn').className =
    tab === 'qr'
      ? 'flex-1 py-2 px-4 text-sm bg-blue-500 text-white'
      : 'flex-1 py-2 px-4 text-sm bg-gray-200 text-gray-700'

  // 切换内容
  document.getElementById('phoneQueryTab').classList.toggle('hidden', tab !== 'phone')
  document.getElementById('qrQueryTab').classList.toggle('hidden', tab !== 'qr')

  // 清空结果
  document.getElementById('miniAppResults').innerHTML = ''
}

function queryByPhone() {
  const phone = document.getElementById('miniAppPhone').value.trim()

  if (!phone) {
    showMiniAppMessage('请输入手机号', 'error')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(phone)) {
    showMiniAppMessage('请输入正确的手机号格式', 'error')
    return
  }

  const invoices = dataManager.getInvoicesByPhone(phone).filter(inv => inv.delivered)

  if (invoices.length === 0) {
    showMiniAppMessage('未找到相关票据', 'info')
    return
  }

  displayMiniAppResults(invoices)
}

function simulateQRScan() {
  // 模拟扫码，随机选择一个已交付的票据
  const invoices = dataManager.getInvoices().filter(inv => inv.delivered)

  if (invoices.length === 0) {
    showMiniAppMessage('暂无可扫描的票据', 'info')
    return
  }

  const randomInvoice = invoices[Math.floor(Math.random() * invoices.length)]
  displayMiniAppResults([randomInvoice])
  showMiniAppMessage('扫码成功！', 'success')
}

function displayMiniAppResults(invoices) {
  const resultsContainer = document.getElementById('miniAppResults')

  resultsContainer.innerHTML = `
        <div class="border-t pt-4">
            <h4 class="font-semibold mb-3">查询结果 (${invoices.length})</h4>
            <div class="space-y-3">
                ${invoices
                  .map(
                    invoice => `
                    <div class="border rounded-lg p-3 bg-gray-50">
                        <div class="flex justify-between items-start mb-2">
                            <div>
                                <p class="font-medium text-sm">${invoice.patientName}</p>
                                <p class="text-xs text-gray-600">${invoice.type} • ${formatDate(
                      invoice.createTime
                    )}</p>
                            </div>
                            <span class="text-sm font-bold text-blue-600">${formatAmount(
                              invoice.amount
                            )}</span>
                        </div>
                        <div class="text-xs text-gray-600 mb-2">
                            <p>票据号：${invoice.id}</p>
                            <p>科室：${invoice.department || '-'}</p>
                        </div>
                        <button onclick="viewMiniAppInvoice('${invoice.id}')"
                                class="w-full bg-blue-500 text-white py-1 px-2 rounded text-xs">
                            查看详情
                        </button>
                    </div>
                `
                  )
                  .join('')}
            </div>
        </div>
    `
}

function viewMiniAppInvoice(id) {
  const invoice = dataManager.getById('invoices', id)
  if (!invoice) {
    showMiniAppMessage('票据不存在', 'error')
    return
  }

  const content = `
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
            <div class="text-center mb-6">
                <h3 class="text-xl font-bold">电子票据详情</h3>
                <p class="text-gray-600 text-sm mt-1">票据号：${invoice.id}</p>
            </div>

            <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">患者姓名：</span>
                    <span class="font-medium">${invoice.patientName}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">票据类型：</span>
                    <span>${invoice.type}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">科室：</span>
                    <span>${invoice.department || '-'}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">医生：</span>
                    <span>${invoice.doctor || '-'}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">开具时间：</span>
                    <span>${formatDate(invoice.createTime)}</span>
                </div>
                <div class="border-t pt-3 mt-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">金额：</span>
                        <span class="text-xl font-bold text-blue-600">${formatAmount(
                          invoice.amount
                        )}</span>
                    </div>
                </div>
            </div>

            <div class="mt-6 space-y-2">
                <button onclick="downloadMiniAppInvoice('${invoice.id}')"
                        class="w-full bg-blue-500 text-white py-2 rounded-lg text-sm">
                    <i class="fas fa-download mr-2"></i>下载票据
                </button>
                <button onclick="closeModal()"
                        class="w-full bg-gray-300 text-gray-700 py-2 rounded-lg text-sm">
                    关闭
                </button>
            </div>
        </div>
    `

  showModal('票据详情', content)
}

function generateQRCode() {
  const invoiceId = document.getElementById('qrInvoiceSelect').value

  if (!invoiceId) {
    showMessage('请选择要生成二维码的票据', 'error')
    return
  }

  const qrContainer = document.getElementById('qrCodeContainer')
  qrContainer.innerHTML =
    '<div class="text-center"><i class="fas fa-spinner fa-spin text-2xl text-blue-500"></i><p class="mt-2">生成中...</p></div>'

  // 生成二维码内容（模拟小程序链接）
  const qrContent = `https://miniapp.example.com/invoice?id=${invoiceId}`

  // 使用QRCode.js生成二维码
  setTimeout(() => {
    qrContainer.innerHTML = `
            <div class="space-y-4">
                <div id="qrcode" class="flex justify-center"></div>
                <p class="text-sm text-gray-600">扫描二维码查看票据</p>
                <p class="text-xs text-gray-500 break-all">${qrContent}</p>
            </div>
        `

    QRCode.toCanvas(
      document.createElement('canvas'),
      qrContent,
      {
        width: 200,
        height: 200,
        margin: 2,
      },
      function (error, canvas) {
        if (error) {
          qrContainer.innerHTML = '<p class="text-red-500">二维码生成失败</p>'
        } else {
          document.getElementById('qrcode').appendChild(canvas)
        }
      }
    )
  }, 1000)
}

function showMiniAppMessage(message, type) {
  const resultsContainer = document.getElementById('miniAppResults')
  const className =
    type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600'

  resultsContainer.innerHTML = `
        <div class="border-t pt-4">
            <p class="text-center text-sm ${className}">${message}</p>
        </div>
    `
}

function downloadMiniAppInvoice(id) {
  const invoice = dataManager.getById('invoices', id)
  if (!invoice) {
    showMiniAppMessage('票据不存在', 'error')
    return
  }

  exportData([invoice], `invoice_${invoice.id}.json`)
  showMessage('票据下载成功', 'success')
  closeModal()
}
