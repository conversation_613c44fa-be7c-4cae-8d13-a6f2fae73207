// 主应用程序文件
class App {
    constructor() {
        this.currentPage = null;
        this.init();
    }

    init() {
        // 检查登录状态
        if (authManager.isLoggedIn()) {
            showDashboard();
        } else {
            showLogin();
        }

        // 绑定键盘事件
        this.bindKeyboardEvents();
    }

    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            // 登录页面回车键登录
            if (e.key === 'Enter' && !document.getElementById('loginPage').classList.contains('hidden')) {
                login();
            }
            
            // ESC键关闭模态框
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    }
}

// 页面显示函数
function showPage(page) {
    // 检查页面访问权限
    if (!checkPageAccess(page)) {
        return;
    }

    // 更新导航高亮
    updateNavHighlight(page);
    
    // 加载页面内容
    const contentArea = document.getElementById('contentArea');
    
    switch(page) {
        case 'dashboard':
            contentArea.innerHTML = getDashboardHTML();
            loadDashboardData();
            break;
        case 'units':
            contentArea.innerHTML = getUnitsHTML();
            loadUnitsData();
            break;
        case 'invoices':
            contentArea.innerHTML = getInvoicesHTML();
            loadInvoicesData();
            break;
        case 'issue':
            contentArea.innerHTML = getIssueHTML();
            initIssueForm();
            break;
        case 'archive':
            contentArea.innerHTML = getArchiveHTML();
            loadArchiveData();
            break;
        case 'delivery':
            contentArea.innerHTML = getDeliveryHTML();
            loadDeliveryData();
            break;
        case 'reports':
            contentArea.innerHTML = getReportsHTML();
            loadReportsData();
            break;
        case 'users':
            contentArea.innerHTML = getUsersHTML();
            loadUsersData();
            break;
        case 'miniapp':
            contentArea.innerHTML = getMiniAppHTML();
            initMiniApp();
            break;
        default:
            contentArea.innerHTML = '<div class="text-center py-8"><h2 class="text-xl text-gray-600">页面未找到</h2></div>';
    }
    
    app.currentPage = page;
}

// 更新导航高亮
function updateNavHighlight(activePage) {
    // 移除所有高亮
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('bg-blue-100', 'text-blue-600');
        item.classList.add('text-gray-700');
    });
    
    // 添加当前页面高亮
    const activeItem = document.querySelector(`[onclick="showPage('${activePage}')"]`);
    if (activeItem) {
        activeItem.classList.add('bg-blue-100', 'text-blue-600');
        activeItem.classList.remove('text-gray-700');
    }
}

// 通用消息显示函数
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${getMessageClass(type)}`;
    messageDiv.innerHTML = `
        <div class="flex items-center">
            <i class="${getMessageIcon(type)} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(messageDiv);
    
    // 自动移除消息
    setTimeout(() => {
        if (messageDiv.parentElement) {
            messageDiv.remove();
        }
    }, 5000);
}

function getMessageClass(type) {
    const classes = {
        'success': 'bg-green-500 text-white',
        'error': 'bg-red-500 text-white',
        'warning': 'bg-yellow-500 text-white',
        'info': 'bg-blue-500 text-white'
    };
    return classes[type] || classes.info;
}

function getMessageIcon(type) {
    const icons = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    };
    return icons[type] || icons.info;
}

// 模态框函数
function showModal(title, content) {
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalContent').innerHTML = content;
    document.getElementById('modal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('modal').classList.add('hidden');
}

// 确认对话框
function showConfirm(message, onConfirm) {
    const content = `
        <div class="mb-4">
            <p class="text-gray-700">${message}</p>
        </div>
        <div class="flex justify-end space-x-3">
            <button onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                取消
            </button>
            <button onclick="closeModal(); (${onConfirm})()" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                确认
            </button>
        </div>
    `;
    showModal('确认操作', content);
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化金额
function formatAmount(amount) {
    return '¥' + parseFloat(amount).toFixed(2);
}

// 生成随机颜色
function getRandomColor() {
    const colors = [
        'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
        'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-gray-500'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
}

// 导出数据为JSON
function exportData(data, filename) {
    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = filename;
    link.click();
}

// 打印功能
function printContent(elementId) {
    const content = document.getElementById(elementId);
    if (content) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>打印</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        .no-print { display: none; }
                    </style>
                </head>
                <body>
                    ${content.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// 搜索功能
function searchTable(inputId, tableId) {
    const input = document.getElementById(inputId);
    const table = document.getElementById(tableId);
    const filter = input.value.toUpperCase();
    const rows = table.getElementsByTagName('tr');
    
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');
        let found = false;
        
        for (let j = 0; j < cells.length; j++) {
            if (cells[j].textContent.toUpperCase().indexOf(filter) > -1) {
                found = true;
                break;
            }
        }
        
        row.style.display = found ? '' : 'none';
    }
}

// 分页功能
class Pagination {
    constructor(data, itemsPerPage = 10) {
        this.data = data;
        this.itemsPerPage = itemsPerPage;
        this.currentPage = 1;
        this.totalPages = Math.ceil(data.length / itemsPerPage);
    }
    
    getCurrentPageData() {
        const start = (this.currentPage - 1) * this.itemsPerPage;
        const end = start + this.itemsPerPage;
        return this.data.slice(start, end);
    }
    
    goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            return true;
        }
        return false;
    }
    
    getPaginationHTML(onPageChange) {
        if (this.totalPages <= 1) return '';
        
        let html = '<div class="flex justify-center items-center space-x-2 mt-4">';
        
        // 上一页
        html += `<button onclick="${onPageChange}(${this.currentPage - 1})" 
                    ${this.currentPage === 1 ? 'disabled' : ''} 
                    class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50">
                    上一页
                 </button>`;
        
        // 页码
        for (let i = 1; i <= this.totalPages; i++) {
            if (i === this.currentPage) {
                html += `<span class="px-3 py-1 bg-blue-500 text-white rounded">${i}</span>`;
            } else {
                html += `<button onclick="${onPageChange}(${i})" class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300">${i}</button>`;
            }
        }
        
        // 下一页
        html += `<button onclick="${onPageChange}(${this.currentPage + 1})" 
                    ${this.currentPage === this.totalPages ? 'disabled' : ''} 
                    class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50">
                    下一页
                 </button>`;
        
        html += '</div>';
        return html;
    }
}

// 初始化应用
const app = new App();
