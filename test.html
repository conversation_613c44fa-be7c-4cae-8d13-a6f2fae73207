<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医疗电子票据管理系统 - 测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-center mb-8 text-blue-600">
                <i class="fas fa-hospital mr-3"></i>
                医疗电子票据管理系统测试
            </h1>
            
            <!-- 测试结果显示 -->
            <div id="testResults" class="space-y-4 mb-8">
                <!-- 测试结果将在这里显示 -->
            </div>
            
            <!-- 测试按钮 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                <button onclick="testDataManager()" class="bg-blue-500 text-white p-4 rounded-lg hover:bg-blue-600 transition">
                    <i class="fas fa-database mr-2"></i>
                    测试数据管理
                </button>
                
                <button onclick="testAuthManager()" class="bg-green-500 text-white p-4 rounded-lg hover:bg-green-600 transition">
                    <i class="fas fa-user-shield mr-2"></i>
                    测试认证系统
                </button>
                
                <button onclick="testInvoiceOperations()" class="bg-yellow-500 text-white p-4 rounded-lg hover:bg-yellow-600 transition">
                    <i class="fas fa-file-invoice mr-2"></i>
                    测试票据操作
                </button>
                
                <button onclick="testUserOperations()" class="bg-purple-500 text-white p-4 rounded-lg hover:bg-purple-600 transition">
                    <i class="fas fa-users mr-2"></i>
                    测试用户操作
                </button>
                
                <button onclick="testUnitOperations()" class="bg-red-500 text-white p-4 rounded-lg hover:bg-red-600 transition">
                    <i class="fas fa-building mr-2"></i>
                    测试单位操作
                </button>
                
                <button onclick="runAllTests()" class="bg-gray-800 text-white p-4 rounded-lg hover:bg-gray-900 transition">
                    <i class="fas fa-play mr-2"></i>
                    运行所有测试
                </button>
            </div>
            
            <!-- 系统链接 -->
            <div class="text-center">
                <a href="index.html" class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition">
                    <i class="fas fa-arrow-right mr-2"></i>
                    进入系统
                </a>
            </div>
        </div>
    </div>

    <!-- 加载核心JavaScript文件 -->
    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    
    <script>
        // 测试工具函数
        function addTestResult(testName, success, message) {
            const resultsContainer = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `p-4 rounded-lg border-l-4 ${success ? 'bg-green-50 border-green-500' : 'bg-red-50 border-red-500'}`;
            
            resultDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${success ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500'} mr-3"></i>
                    <div>
                        <h3 class="font-semibold ${success ? 'text-green-800' : 'text-red-800'}">${testName}</h3>
                        <p class="text-sm ${success ? 'text-green-600' : 'text-red-600'}">${message}</p>
                    </div>
                </div>
            `;
            
            resultsContainer.appendChild(resultDiv);
        }
        
        function clearTestResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        // 测试数据管理器
        function testDataManager() {
            clearTestResults();
            
            try {
                // 测试单位管理
                const testUnit = {
                    name: '测试医院',
                    address: '测试地址123号',
                    contact: '测试联系人',
                    phone: '0571-12345678'
                };
                
                const addedUnit = dataManager.addUnit(testUnit);
                if (addedUnit && addedUnit.id) {
                    addTestResult('单位添加', true, `成功添加单位，ID: ${addedUnit.id}`);
                } else {
                    addTestResult('单位添加', false, '添加单位失败');
                    return;
                }
                
                // 测试单位查询
                const foundUnit = dataManager.getById('units', addedUnit.id);
                if (foundUnit && foundUnit.name === testUnit.name) {
                    addTestResult('单位查询', true, '成功查询到添加的单位');
                } else {
                    addTestResult('单位查询', false, '查询单位失败');
                }
                
                // 测试单位更新
                const updateData = { name: '更新后的医院名称' };
                const updatedUnit = dataManager.updateUnit(addedUnit.id, updateData);
                if (updatedUnit && updatedUnit.name === updateData.name) {
                    addTestResult('单位更新', true, '成功更新单位信息');
                } else {
                    addTestResult('单位更新', false, '更新单位失败');
                }
                
                // 测试单位删除
                const deleteResult = dataManager.deleteUnit(addedUnit.id);
                if (deleteResult) {
                    addTestResult('单位删除', true, '成功删除单位');
                } else {
                    addTestResult('单位删除', false, '删除单位失败');
                }
                
            } catch (error) {
                addTestResult('数据管理测试', false, `测试过程中出现错误: ${error.message}`);
            }
        }
        
        // 测试认证管理器
        function testAuthManager() {
            clearTestResults();
            
            try {
                // 测试登录
                const loginResult = authManager.login('admin');
                if (loginResult.success) {
                    addTestResult('用户登录', true, `成功登录，用户: ${loginResult.user.name}`);
                } else {
                    addTestResult('用户登录', false, loginResult.message);
                    return;
                }
                
                // 测试权限检查
                const hasPermission = authManager.hasPermission('*');
                if (hasPermission) {
                    addTestResult('权限检查', true, '管理员拥有所有权限');
                } else {
                    addTestResult('权限检查', false, '权限检查失败');
                }
                
                // 测试页面访问权限
                const canAccess = authManager.canAccessPage('users');
                if (canAccess) {
                    addTestResult('页面访问权限', true, '可以访问用户管理页面');
                } else {
                    addTestResult('页面访问权限', false, '无法访问用户管理页面');
                }
                
                // 测试登出
                authManager.logout();
                if (!authManager.isLoggedIn()) {
                    addTestResult('用户登出', true, '成功登出系统');
                } else {
                    addTestResult('用户登出', false, '登出失败');
                }
                
            } catch (error) {
                addTestResult('认证系统测试', false, `测试过程中出现错误: ${error.message}`);
            }
        }
        
        // 测试票据操作
        function testInvoiceOperations() {
            clearTestResults();
            
            try {
                // 测试票据添加
                const testInvoice = {
                    patientName: '测试患者',
                    patientId: '330101199001011234',
                    phone: '13800138001',
                    type: '门诊',
                    amount: 100.50,
                    department: '内科',
                    doctor: '测试医生'
                };
                
                const addedInvoice = dataManager.addInvoice(testInvoice);
                if (addedInvoice && addedInvoice.id) {
                    addTestResult('票据添加', true, `成功添加票据，ID: ${addedInvoice.id}`);
                } else {
                    addTestResult('票据添加', false, '添加票据失败');
                    return;
                }
                
                // 测试票据查询
                const foundInvoice = dataManager.getById('invoices', addedInvoice.id);
                if (foundInvoice && foundInvoice.patientName === testInvoice.patientName) {
                    addTestResult('票据查询', true, '成功查询到添加的票据');
                } else {
                    addTestResult('票据查询', false, '查询票据失败');
                }
                
                // 测试按手机号查询
                const phoneInvoices = dataManager.getInvoicesByPhone(testInvoice.phone);
                if (phoneInvoices.length > 0) {
                    addTestResult('手机号查询', true, `找到 ${phoneInvoices.length} 张票据`);
                } else {
                    addTestResult('手机号查询', false, '按手机号查询失败');
                }
                
                // 测试票据状态更新
                const updateResult = dataManager.updateInvoice(addedInvoice.id, { status: '已冲红' });
                if (updateResult && updateResult.status === '已冲红') {
                    addTestResult('票据状态更新', true, '成功更新票据状态为已冲红');
                } else {
                    addTestResult('票据状态更新', false, '更新票据状态失败');
                }
                
            } catch (error) {
                addTestResult('票据操作测试', false, `测试过程中出现错误: ${error.message}`);
            }
        }
        
        // 测试用户操作
        function testUserOperations() {
            clearTestResults();
            
            try {
                // 测试用户添加
                const testUser = {
                    username: 'testuser',
                    name: '测试用户',
                    role: '医生'
                };
                
                const addedUser = dataManager.addUser(testUser);
                if (addedUser) {
                    addTestResult('用户添加', true, `成功添加用户: ${testUser.username}`);
                } else {
                    addTestResult('用户添加', false, '添加用户失败');
                    return;
                }
                
                // 测试用户查询
                const foundUser = dataManager.getUserByUsername(testUser.username);
                if (foundUser && foundUser.name === testUser.name) {
                    addTestResult('用户查询', true, '成功查询到添加的用户');
                } else {
                    addTestResult('用户查询', false, '查询用户失败');
                }
                
                // 测试用户更新
                const updateResult = dataManager.updateUser(testUser.username, { name: '更新后的用户名' });
                if (updateResult && updateResult.name === '更新后的用户名') {
                    addTestResult('用户更新', true, '成功更新用户信息');
                } else {
                    addTestResult('用户更新', false, '更新用户失败');
                }
                
                // 测试用户删除
                const deleteResult = dataManager.deleteUser(testUser.username);
                if (deleteResult) {
                    addTestResult('用户删除', true, '成功删除用户');
                } else {
                    addTestResult('用户删除', false, '删除用户失败');
                }
                
            } catch (error) {
                addTestResult('用户操作测试', false, `测试过程中出现错误: ${error.message}`);
            }
        }
        
        // 测试单位操作
        function testUnitOperations() {
            clearTestResults();
            
            try {
                const initialUnits = dataManager.getUnits();
                addTestResult('初始数据检查', true, `系统中有 ${initialUnits.length} 个单位`);
                
                // 测试统计功能
                const stats = dataManager.getStatistics();
                addTestResult('统计数据', true, `总票据: ${stats.totalInvoices}, 总金额: ¥${stats.totalAmount.toFixed(2)}`);
                
                // 测试数据完整性
                const invoices = dataManager.getInvoices();
                const users = dataManager.getUsers();
                
                if (invoices.length > 0 && users.length > 0) {
                    addTestResult('数据完整性', true, `票据: ${invoices.length}张, 用户: ${users.length}个`);
                } else {
                    addTestResult('数据完整性', false, '数据不完整');
                }
                
            } catch (error) {
                addTestResult('单位操作测试', false, `测试过程中出现错误: ${error.message}`);
            }
        }
        
        // 运行所有测试
        function runAllTests() {
            clearTestResults();
            
            addTestResult('开始测试', true, '正在运行所有测试用例...');
            
            setTimeout(() => {
                testDataManager();
                setTimeout(() => {
                    testAuthManager();
                    setTimeout(() => {
                        testInvoiceOperations();
                        setTimeout(() => {
                            testUserOperations();
                            setTimeout(() => {
                                testUnitOperations();
                                setTimeout(() => {
                                    addTestResult('测试完成', true, '所有测试用例执行完毕');
                                }, 500);
                            }, 500);
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }
    </script>
</body>
</html>
