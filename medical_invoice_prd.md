# 医疗电子票据管理系统 PRD（产品需求文档）

## 一、项目背景
为提升医疗机构票据管理效率，实现电子化、智能化、标准化管理，开发一套支持电子票据全生命周期管理的平台系统。由于项目初期阶段，所有数据暂存使用 LocalStorage 模拟，便于快速开发与原型验证。

---

## 二、系统目标

- 支持医疗票据的申领、开具、冲红、归档、交付与查询。
- 实现电子票据多渠道交付（如短信、小程序、APP等）。
- 支持本地单位信息维护与财政接口数据同步。
- 提供完善的用户权限与系统配置管理。
- 支持个性化微信小程序扫码取票功能。

---

## 三、系统模块概述

### 1. 基础信息管理
- 从财政同步单位、开票点等基础信息。
- 本地维护单位信息（含单位代码、地址、联系人等）。
- 使用 LocalStorage 存储单位信息列表（JSON 格式）。

### 2. 票据管理模块
- 支持票据申领、分发、申退、审验、销毁流程。
- 每张票据存储票据编号、状态、分配单位等字段。
- 所有票据记录保存在 LocalStorage（以数组方式管理）。

### 3. 票据开具模块
- 与 HIS 对接（模拟 API），支持门诊/住院票据开具。
- 票据支持冲红、打印、补打。
- 票据格式为 JSON，包含开票内容、金额、患者信息、状态等字段。
- 每次开具后将票据写入 LocalStorage，并生成唯一票据 ID。

### 4. 票据存档模块
- 支持将票据下载归档（模拟财政归档接口）。
- 将归档票据以标记 `archived=true` 方式存储于 LocalStorage 中。
- 支持本地查看归档票据列表与详情。

### 5. 票据交付模块
- 提供模拟交付功能，如：生成短信内容、小程序链接。
- 在 LocalStorage 中保存交付记录（含票据ID、用户手机号、交付方式等）。

### 6. 综合报表与查询模块
- 查询各类票据的库存、使用、冲红、作废等数据。
- 使用 JS 在本地汇总 LocalStorage 中数据进行统计展示。

### 7. 系统管理模块
- 用户登录管理（模拟，无需密码，仅使用用户名）。
- 用户角色管理：管理员、医生、财务等角色。
- 权限控制由前端控制页面访问权限（基于角色判断）。

### 8. 取票小程序（模拟）
- 模拟生成二维码（内容为票据ID）。
- 支持输入手机号或扫码获取已交付票据。
- LocalStorage 中票据查找规则为手机号匹配。

---

## 四、核心数据结构（LocalStorage 示例）

### 1. 单位信息
```json
localStorage.setItem('units', JSON.stringify([
  {
    "id": "001",
    "name": "第一人民医院",
    "address": "xx路88号",
    "contact": "张主任"
  }
]))
```

### 2. 票据记录
```json
localStorage.setItem('invoices', JSON.stringify([
  {
    "id": "INV20250709-001",
    "patientName": "李雷",
    "type": "门诊",
    "amount": 200.0,
    "status": "已开具",
    "archived": false,
    "delivered": false
  }
]))
```

### 3. 用户信息
```json
localStorage.setItem('users', JSON.stringify([
  {
    "username": "admin",
    "role": "管理员"
  },
  {
    "username": "doctor1",
    "role": "医生"
  }
]))
```

---

## 五、页面功能列表

| 页面名称     | 功能说明                           |
|------------|----------------------------------|
| 登录页       | 输入用户名模拟登录，保存当前用户角色         |
| 单位信息管理   | 增删改查单位信息，支持本地录入                 |
| 票据管理     | 查看所有票据状态，支持申领、分发、销毁等操作       |
| 开票页面     | 输入就诊信息并生成票据，支持冲红/补打/打印功能    |
| 存档票据查询   | 查询已归档票据，模拟从财政接口获取                |
| 交付页面     | 模拟票据通过短信/二维码等方式交付              |
| 报表页面     | 显示票据汇总数据图表、列表                     |
| 用户与权限管理 | 增删用户，配置角色与页面访问权限                |
| 小程序模拟     | 输入手机号/扫码查看票据                      |

---

## 六、页面原型设计（HTML + Tailwind 示例）

### 登录页面 login.html
```html
<body class="flex h-screen items-center justify-center bg-gray-100">
  <div class="bg-white p-8 rounded-xl shadow-xl w-80">
    <h2 class="text-2xl font-bold text-center mb-6">登录系统</h2>
    <input type="text" id="username" class="w-full p-2 border rounded mb-4" placeholder="请输入用户名" />
    <button onclick="login()" class="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600">登录</button>
  </div>
  <script>
    function login() {
      const user = document.getElementById('username').value;
      localStorage.setItem('currentUser', user);
      window.location.href = 'dashboard.html';
    }
  </script>
</body>
```

### 单位管理页面 unit.html（局部）
```html
<div class="p-4">
  <h2 class="text-xl font-bold mb-4">单位信息</h2>
  <table class="w-full border">
    <thead class="bg-gray-200">
      <tr><th>ID</th><th>名称</th><th>地址</th><th>联系人</th></tr>
    </thead>
    <tbody id="unitTable"></tbody>
  </table>
</div>
<script>
  const units = JSON.parse(localStorage.getItem('units') || '[]');
  document.getElementById('unitTable').innerHTML = units.map(u => `
    <tr class='border text-center'><td>${u.id}</td><td>${u.name}</td><td>${u.address}</td><td>${u.contact}</td></tr>
  `).join('');
</script>
```

---

## 七、页面跳转流程图（简化版）
```
[登录页面]
    ↓ 成功登录
[首页仪表盘 dashboard.html]
    ├─> 单位信息管理 unit.html
    ├─> 票据管理 invoice.html
    ├─> 开票页面 issue.html
    ├─> 存档查询 archive.html
    ├─> 交付页面 delivery.html
    ├─> 报表页面 report.html
    └─> 小程序取票小票 mini.html
```

---

如需更多页面HTML、交互细节，或需要整合为 SPA 应用结构，可继续提出需求。

