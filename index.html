<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>医疗电子票据管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
  </head>
  <body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-blue-600 text-white shadow-lg">
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <i class="fas fa-hospital text-2xl"></i>
            <h1 class="text-xl font-bold">医疗电子票据管理系统</h1>
          </div>
          <div class="flex items-center space-x-4">
            <span id="currentUserDisplay" class="text-sm"></span>
            <button
              onclick="logout()"
              class="bg-red-500 hover:bg-red-600 px-3 py-1 rounded text-sm"
            >
              <i class="fas fa-sign-out-alt mr-1"></i>退出
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 py-6">
      <!-- 登录页面 -->
      <div id="loginPage" class="hidden">
        <div class="flex h-screen items-center justify-center">
          <div class="bg-white p-8 rounded-xl shadow-xl w-96">
            <div class="text-center mb-6">
              <i class="fas fa-hospital text-4xl text-blue-600 mb-4"></i>
              <h2 class="text-2xl font-bold text-gray-800">系统登录</h2>
              <p class="text-gray-600 mt-2">医疗电子票据管理系统</p>
            </div>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                <input
                  type="text"
                  id="username"
                  class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入用户名"
                />
              </div>
              <button
                onclick="login()"
                class="w-full bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 transition duration-200"
              >
                <i class="fas fa-sign-in-alt mr-2"></i>登录
              </button>
            </div>
            <div class="mt-6 text-sm text-gray-500">
              <p>测试账号：</p>
              <p>• admin (管理员)</p>
              <p>• doctor1 (医生)</p>
              <p>• finance1 (财务)</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 主仪表盘 -->
      <div id="dashboardPage" class="hidden">
        <!-- 侧边栏导航 -->
        <div class="flex">
          <div class="w-64 bg-white shadow-lg rounded-lg mr-6">
            <div class="p-4">
              <h3 class="text-lg font-semibold text-gray-800 mb-4">功能菜单</h3>
              <nav class="space-y-2">
                <a
                  href="#"
                  onclick="showPage('dashboard')"
                  class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600"
                >
                  <i class="fas fa-tachometer-alt mr-3"></i>仪表盘
                </a>
                <a
                  href="#"
                  onclick="showPage('units')"
                  class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600"
                >
                  <i class="fas fa-building mr-3"></i>单位管理
                </a>
                <a
                  href="#"
                  onclick="showPage('invoices')"
                  class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600"
                >
                  <i class="fas fa-file-invoice mr-3"></i>票据管理
                </a>
                <a
                  href="#"
                  onclick="showPage('issue')"
                  class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600"
                >
                  <i class="fas fa-plus-circle mr-3"></i>开具票据
                </a>
                <a
                  href="#"
                  onclick="showPage('archive')"
                  class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600"
                >
                  <i class="fas fa-archive mr-3"></i>存档查询
                </a>
                <a
                  href="#"
                  onclick="showPage('delivery')"
                  class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600"
                >
                  <i class="fas fa-paper-plane mr-3"></i>票据交付
                </a>
                <a
                  href="#"
                  onclick="showPage('reports')"
                  class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600"
                >
                  <i class="fas fa-chart-bar mr-3"></i>统计报表
                </a>
                <a
                  href="#"
                  onclick="showPage('users')"
                  class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600"
                >
                  <i class="fas fa-users mr-3"></i>用户管理
                </a>
                <a
                  href="#"
                  onclick="showPage('miniapp')"
                  class="nav-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600"
                >
                  <i class="fas fa-qrcode mr-3"></i>小程序取票
                </a>
              </nav>
            </div>
          </div>

          <!-- 主内容区域 -->
          <div class="flex-1">
            <div id="contentArea">
              <!-- 内容将通过JavaScript动态加载 -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通用模态框 -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
      <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
          <div class="p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 id="modalTitle" class="text-lg font-semibold"></h3>
              <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div id="modalContent"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载核心JavaScript文件 -->
    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/pages.js"></script>
    <script src="js/pages-extended.js"></script>
    <script src="js/pages-final.js"></script>
  </body>
</html>
