<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow p-6">
        <h1 class="text-2xl font-bold mb-6">JavaScript 加载调试</h1>
        
        <div id="debugInfo" class="space-y-4">
            <p>正在检查JavaScript文件加载状态...</p>
        </div>
        
        <button onclick="testFunctions()" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded">
            测试函数
        </button>
    </div>

    <!-- 按顺序加载JavaScript文件 -->
    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/pages.js"></script>
    <script src="js/pages-extended.js"></script>
    <script src="js/pages-final.js"></script>
    
    <script>
        function addDebugInfo(message, isError = false) {
            const debugDiv = document.getElementById('debugInfo');
            const p = document.createElement('p');
            p.className = isError ? 'text-red-600' : 'text-green-600';
            p.textContent = message;
            debugDiv.appendChild(p);
        }
        
        function testFunctions() {
            const debugDiv = document.getElementById('debugInfo');
            debugDiv.innerHTML = '<h3 class="font-bold">函数检查结果：</h3>';
            
            // 检查各个函数是否存在
            const functions = [
                'getDashboardHTML',
                'getUnitsHTML', 
                'getInvoicesHTML',
                'getIssueHTML',
                'getArchiveHTML',
                'getDeliveryHTML',
                'getReportsHTML',
                'getUsersHTML',
                'getMiniAppHTML'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addDebugInfo(`✓ ${funcName} 函数存在`);
                } else {
                    addDebugInfo(`✗ ${funcName} 函数不存在`, true);
                }
            });
            
            // 检查数据管理器
            if (typeof dataManager !== 'undefined') {
                addDebugInfo('✓ dataManager 已加载');
            } else {
                addDebugInfo('✗ dataManager 未加载', true);
            }
            
            // 检查认证管理器
            if (typeof authManager !== 'undefined') {
                addDebugInfo('✓ authManager 已加载');
            } else {
                addDebugInfo('✗ authManager 未加载', true);
            }
            
            // 测试调用 getDashboardHTML
            try {
                const html = getDashboardHTML();
                if (html && html.length > 0) {
                    addDebugInfo('✓ getDashboardHTML 调用成功');
                } else {
                    addDebugInfo('✗ getDashboardHTML 返回空内容', true);
                }
            } catch (error) {
                addDebugInfo(`✗ getDashboardHTML 调用失败: ${error.message}`, true);
            }
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            setTimeout(testFunctions, 1000);
        });
    </script>
</body>
</html>
